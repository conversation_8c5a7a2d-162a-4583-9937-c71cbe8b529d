<route lang="json">
{
  "style": {
    "navigationBarTitleText": "聊天",
    "navigationBarBackgroundColor": "#ffffff",
    "navigationBarTextStyle": "black",
    "app-plus": {
      "titleNView": {
        "buttons": [
          {
            "text": "设置",
            "fontSize": "16px",
            "color": "#333333"
          }
        ]
      }
    }
  }
}
</route>
<template>
  <c-page>
    <view class="chat-container">
      <!-- 聊天消息列表 -->
      <scroll-view
        scroll-y
        class="message-list"
        :scroll-top="scrollTop"
        scroll-with-animation
        @scrolltoupper="loadMoreMessages"
      >
        <!-- 加载更多提示 -->
        <view v-if="isLoadingMore" class="loading-more">
          <text class="loading-text">加载中...</text>
        </view>

        <!-- 消息列表 -->
        <view v-for="(message, index) in messageList" :key="message.id" class="message-item">
          <!-- 时间分割线 -->
          <view v-if="shouldShowTime(message, messageList[index - 1])" class="time-divider">
            <text class="time-text">{{ formatTime(message.timestamp) }}</text>
          </view>

          <!-- 消息内容 -->
          <view class="message-wrapper" :class="{ 'own-message': message.isOwn }">
            <!-- 对方消息 -->
            <view v-if="!message.isOwn" class="message-row">
              <view class="avatar">
                <image :src="message.avatar" class="avatar-img" mode="aspectFill" />
              </view>
              <view class="message-content">
                <view v-if="chatInfo.type === 'GROUP'" class="sender-name">
                  <text class="name-text">{{ message.nickname }}</text>
                </view>
                <view class="message-bubble other-bubble">
                  <text class="message-text">{{ message.content }}</text>
                </view>
              </view>
            </view>

            <!-- 自己的消息 -->
            <view v-else class="message-row own-row">
              <view class="message-content own-content">
                <view class="message-bubble own-bubble">
                  <text class="message-text">{{ message.content }}</text>
                </view>
                <!-- 已读状态 -->
                <view v-if="message.isRead !== undefined" class="read-status">
                  <text class="read-text">{{ getReadStatusText(message) }}</text>
                </view>
              </view>
              <view class="avatar">
                <image :src="currentUser.avatar" class="avatar-img" mode="aspectFill" />
              </view>
            </view>
          </view>
        </view>
      </scroll-view>

      <!-- 输入区域 -->
      <view class="input-area">
        <!-- 主输入行 -->
        <view class="input-container">
          <!-- 语音按钮 -->
          <view class="voice-btn" @click="toggleVoiceMode">
            <text class="quick-reply-text">语</text>
          </view>
          <!-- 常用语按钮 -->
          <view class="quick-reply-btn" @click="toggleQuickReplyPanel">
            <image
              :src="isVoiceMode ? '/static/images/im/cyy.png' : '/static/images/im/cyy.png'"
              class="btn-icon"
              mode="aspectFit"
            />
          </view>
          <!-- 输入框 -->
          <view class="input-wrapper">
            <!-- 文本输入模式 -->
            <view v-if="!isVoiceMode" class="text-input-container">
              <input
                v-model="inputText"
                class="message-input"
                placeholder="想对我说点什么..."
                confirm-type="send"
                @confirm="sendMessage"
                @focus="onInputFocus"
                @blur="onInputBlur"
              />
            </view>
            <!-- 语音输入按钮 -->
            <view
              v-else
              class="voice-input-btn"
              @touchstart="startVoiceRecord"
              @touchend="endVoiceRecord"
            >
              <text class="voice-input-text">{{ isRecording ? '松开发送' : '按住说话' }}</text>
            </view>
          </view>

          <!-- 表情按钮 -->
          <view class="emoji-btn" @click="toggleEmojiPanel">
            <image src="/static/images/im/icon.png" class="btn-icon" mode="aspectFit" />
          </view>

          <!-- 更多功能按钮 -->
          <view class="more-btn" @click="toggleMorePanel">
            <image src="/static/images/im/fj.png" class="btn-icon" mode="aspectFit" />
          </view>

          <!-- 发送按钮 -->
          <!-- <view v-if="inputText.trim()" class="send-btn" @click="sendMessage">
            <text class="send-text">发送</text>
          </view> -->
        </view>

        <!-- 表情面板 -->
        <view v-if="showEmojiPanel" class="emoji-panel">
          <view class="emoji-grid">
            <view
              v-for="emoji in emojiList"
              :key="emoji"
              class="emoji-item"
              @click="insertEmoji(emoji)"
            >
              <text class="emoji-text">{{ emoji }}</text>
            </view>
          </view>
        </view>

        <!-- 常用语面板 -->
        <view v-if="showQuickReplyPanel" class="quick-reply-panel">
          <view class="quick-reply-grid">
            <view
              v-for="reply in quickReplyList"
              :key="reply"
              class="quick-reply-item"
              @click="selectQuickReply(reply)"
            >
              <text class="quick-reply-item-text">{{ reply }}</text>
            </view>
          </view>
        </view>

        <!-- 更多功能面板 -->
        <view v-if="showMorePanel" class="more-panel">
          <view class="more-grid">
            <view class="more-item" @click="selectImage">
              <view class="more-icon">
                <image src="/static/images/im/icon.png" class="more-icon-img" mode="aspectFit" />
              </view>
              <text class="more-text">图片</text>
            </view>
            <view class="more-item" @click="selectFile">
              <view class="more-icon">
                <image src="/static/images/im/fj.png" class="more-icon-img" mode="aspectFit" />
              </view>
              <text class="more-text">文件</text>
            </view>
            <view class="more-item" @click="selectLocation">
              <view class="more-icon">
                <image src="/static/images/im/icon.png" class="more-icon-img" mode="aspectFit" />
              </view>
              <text class="more-text">位置</text>
            </view>
            <view class="more-item" @click="selectContact">
              <view class="more-icon">
                <image src="/static/images/user/tx.png" class="more-icon-img" mode="aspectFit" />
              </view>
              <text class="more-text">名片</text>
            </view>
            <!-- 开发测试功能 -->
            <view class="more-item" @click="testPersonalChat">
              <view class="more-icon">
                <text class="more-icon-text">👤</text>
              </view>
              <text class="more-text">个人设置</text>
            </view>
            <view class="more-item" @click="testGroupChat">
              <view class="more-icon">
                <text class="more-icon-text">👥</text>
              </view>
              <text class="more-text">群聊信息</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 未读消息提醒弹窗 -->
      <view v-if="showUnreadAlert" class="unread-alert-overlay" @click="hideUnreadAlert">
        <view class="unread-alert" @click.stop>
          <view class="alert-content">
            <text class="alert-title">未读</text>
            <text class="alert-message">对方超过10分钟未读，建议电话联系</text>
            <view class="alert-actions">
              <view class="alert-btn cancel-btn" @click="hideUnreadAlert">
                <text class="btn-text">取消</text>
              </view>
              <view class="alert-btn call-btn" @click="makePhoneCall">
                <text class="btn-text">立即拨打</text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </c-page>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, onUnmounted, nextTick } from 'vue'
import { onLoad, onNavigationBarButtonTap } from '@dcloudio/uni-app'
import TencentCloudChat from '@tencentcloud/chat'
import imManager from '@/utils/im/index'

// 定义接口 - 使用腾讯云IM SDK的标准Message接口
interface ChatMessage {
  ID: string
  type: string
  payload: any
  conversationID: string
  conversationType: string
  to: string
  from: string
  flow: 'in' | 'out'
  time: number
  status: string
  isRevoked: boolean
  isPeerRead: boolean
  nick: string
  avatar: string
  nameCard: string
  elements: any[]
}

// 为了兼容现有UI，定义一个转换后的Message接口
interface Message {
  id: string
  type: 'text' | 'image' | 'voice'
  content: string
  from: string
  to: string
  timestamp: number
  isOwn: boolean
  avatar: string
  nickname: string
  isRead?: boolean
  readCount?: number
}

interface ChatInfo {
  id: string
  name: string
  avatar: string
  type: 'C2C' | 'GROUP'
  isOnline?: boolean
  memberCount?: number
}

interface CurrentUser {
  id: string
  nickname: string
  avatar: string
}

// 响应式数据
const messageList = ref<Message[]>([])
const inputText = ref('')
const scrollTop = ref(0)
const isLoadingMore = ref(false)
const nextReqMessageID = ref('')
const isCompleted = ref(false)
const pageOptions = ref<any>({})

// 输入区域状态
const isVoiceMode = ref(false)
const isRecording = ref(false)
const showEmojiPanel = ref(false)
const showMorePanel = ref(false)
const showQuickReplyPanel = ref(false)

// 未读消息提醒状态
const showUnreadAlert = ref(false)
const unreadTimer = ref<number | null>(null)
const unreadCount = ref(0)

// 表情列表
const emojiList = ref([
  '😀',
  '😃',
  '😄',
  '😁',
  '😆',
  '😅',
  '😂',
  '🤣',
  '😊',
  '😇',
  '🙂',
  '🙃',
  '😉',
  '😌',
  '😍',
  '🥰',
  '😘',
  '😗',
  '😙',
  '😚',
  '😋',
  '😛',
  '😝',
  '😜',
  '🤪',
  '🤨',
  '🧐',
  '🤓',
  '😎',
  '🤩',
  '🥳',
  '😏',
  '😒',
  '😞',
  '😔',
  '😟',
  '😕',
  '🙁',
  '☹️',
  '😣',
  '😖',
  '😫',
  '😩',
  '🥺',
  '😢',
  '😭',
  '😤',
  '😠',
  '😡',
  '🤬',
  '🤯',
  '😳',
  '🥵',
  '🥶',
  '😱',
  '😨',
  '😰',
  '😥',
  '😓',
  '🤗',
  '🤔',
  '🤭',
  '🤫',
  '🤥',
  '😶',
  '😐',
  '😑',
  '😬',
  '🙄',
  '😯',
  '😦',
  '😧',
  '😮',
  '😲',
  '🥱',
  '😴',
  '🤤',
  '😪',
  '😵',
  '🤐',
])

// 常用语列表
const quickReplyList = ref([
  '你好',
  '谢谢',
  '不客气',
  '好的',
  '没问题',
  '稍等一下',
  '我知道了',
  '辛苦了',
  '再见',
  '晚安',
  '早上好',
  '下午好',
  '晚上好',
  '周末愉快',
  '节日快乐',
  '生日快乐',
])

const chatInfo = reactive<ChatInfo>({
  id: '',
  name: '',
  avatar: '/static/images/user/default_avatar.png',
  type: 'C2C',
})

const currentUser = reactive<CurrentUser>({
  id: '',
  nickname: '',
  avatar: '/static/images/user/default_avatar.png',
})

// 计算属性
const conversationID = computed(() => {
  if (chatInfo.type === 'GROUP') {
    return `GROUP${chatInfo.id}`
  } else {
    return `C2C${chatInfo.id}`
  }
})

// 生命周期
onLoad((options) => {
  pageOptions.value = options || {}
})

onMounted(async () => {
  await initChat()
  await initializeIM()
  await loadMessages()
  setupMessageListener()
  markMessagesAsRead()
})

onUnmounted(() => {
  removeMessageListener()
  clearUnreadTimer()
})

// 处理导航栏按钮点击事件
onNavigationBarButtonTap((e) => {
  if (e.index === 0) {
    // 设置按钮
    goToSettings()
  }
})

// 初始化聊天
const initChat = async () => {
  const options = pageOptions.value

  // 获取聊天参数
  if (options.userID) {
    chatInfo.id = options.userID
    chatInfo.type = 'C2C'
    chatInfo.name = decodeURIComponent(options.username || '用户')
    chatInfo.avatar = decodeURIComponent(options.avatar || '/static/images/user/default_avatar.png')
  } else if (options.groupId) {
    chatInfo.id = options.groupId
    chatInfo.type = 'GROUP'
    chatInfo.name = decodeURIComponent(options.groupName || '群聊')
  } else {
    // 如果没有参数，使用默认聊天信息
    chatInfo.id = 'default_user'
    chatInfo.type = 'C2C'
    chatInfo.name = '默认用户'
    chatInfo.avatar = '/static/images/user/tx.png'
  }

  // 设置页面标题
  uni.setNavigationBarTitle({
    title: chatInfo.name,
  })

  // 检查IM登录状态
  if (!imManager.isReady) {
    console.warn('IM未登录')
  }
}

// 初始化IM
const initializeIM = async () => {
  try {
    // 初始化IM SDK
    const initSuccess = await imManager.init()
    if (!initSuccess) {
      console.error('IM初始化失败')
      uni.showToast({
        title: 'IM初始化失败',
        icon: 'error',
      })
      return
    }

    // 如果未登录，尝试自动登录
    if (!imManager.isReady) {
      try {
        const loginSuccess = await imManager.autoLogin()
        if (!loginSuccess) {
          console.error('IM自动登录失败')
          uni.showToast({
            title: 'IM登录失败，聊天功能可能无法正常使用',
            icon: 'none',
            duration: 3000,
          })
        }
      } catch (error) {
        console.error('IM自动登录异常:', error)
        uni.showToast({
          title: 'IM登录失败，请检查用户信息',
          icon: 'none',
          duration: 3000,
        })
      }
    }
  } catch (error) {
    console.error('初始化IM异常:', error)
    uni.showToast({
      title: 'IM初始化异常',
      icon: 'error',
    })
  }
}

// 加载消息 - 使用腾讯云IM SDK标准API
const loadMessages = async () => {
  try {
    if (!imManager.isReady) {
      console.error('IM SDK未初始化')
      return
    }

    const result = await imManager.getMessageList(conversationID.value)

    const newMessages = result.messageList.map((msg: any) => formatMessage(msg))

    if (nextReqMessageID.value) {
      // 加载更多消息，插入到列表前面
      messageList.value = [...newMessages, ...messageList.value]
    } else {
      // 首次加载消息
      messageList.value = newMessages
      scrollToBottom()
    }

    nextReqMessageID.value = result.nextReqMessageID
    isCompleted.value = result.isCompleted
  } catch (error) {
    console.error('加载消息失败:', error)
    uni.showToast({
      title: '加载消息失败',
      icon: 'error',
    })
  }
}

// 加载更多消息
const loadMoreMessages = async () => {
  if (isLoadingMore.value || isCompleted.value) return

  isLoadingMore.value = true
  await loadMessages()
  isLoadingMore.value = false
}

// 发送消息 - 使用腾讯云IM SDK标准API
const sendMessage = async () => {
  if (!inputText.value.trim()) return

  // 检查IM是否就绪
  if (!imManager.isReady) {
    uni.showToast({
      title: 'IM未就绪，请稍后再试',
      icon: 'none',
    })
    return
  }

  const text = inputText.value.trim()
  inputText.value = ''

  try {
    // 发送文本消息
    const result = await imManager.sendMessage(chatInfo.id, text, chatInfo.type)

    if (result.code === 0) {
      // 消息发送成功，通过MESSAGE_RECEIVED事件会自动添加到列表
      scrollToBottom()
    } else {
      console.error('发送消息失败:', result.message)
      uni.showToast({
        title: `发送失败: ${result.message}`,
        icon: 'error',
      })
    }
  } catch (error) {
    console.error('发送消息异常:', error)
    uni.showToast({
      title: '发送失败',
      icon: 'error',
    })
  }
}

// 格式化腾讯云IM消息为UI消息格式
const formatMessage = (chatMessage: ChatMessage): Message => {
  let content = ''
  let type: 'text' | 'image' | 'voice' = 'text'

  // 根据消息类型处理内容
  if (chatMessage.type === TencentCloudChat.TYPES.MSG_TEXT) {
    content = chatMessage.payload?.text || '[文本消息]'
    type = 'text'
  } else if (chatMessage.type === TencentCloudChat.TYPES.MSG_IMAGE) {
    content = '[图片]'
    type = 'image'
  } else if (chatMessage.type === TencentCloudChat.TYPES.MSG_AUDIO) {
    content = '[语音]'
    type = 'voice'
  } else {
    content = '[暂不支持的消息类型]'
    type = 'text'
  }

  return {
    id: chatMessage.ID,
    type: type,
    content: content,
    from: chatMessage.from,
    to: chatMessage.to,
    timestamp: chatMessage.time,
    isOwn: chatMessage.flow === 'out',
    avatar: chatMessage.avatar || '/static/images/user/default_avatar.png',
    nickname: chatMessage.nick || chatMessage.from,
    isRead: chatMessage.isPeerRead,
  }
}

// 设置消息监听器
const setupMessageListener = () => {
  if (!imManager.isReady) return

  imManager.addMessageListener(onMessageReceived)
}

// 移除消息监听器
const removeMessageListener = () => {
  if (!imManager.isReady) return

  imManager.removeMessageListener(onMessageReceived)
}

// 接收到新消息
const onMessageReceived = (event: any) => {
  const messages = event.data
  messages.forEach((message: ChatMessage) => {
    // 只处理当前会话的消息
    if (message.conversationID === conversationID.value) {
      // 检查消息是否已存在
      const existingMessage = messageList.value.find((msg) => msg.id === message.ID)
      if (!existingMessage) {
        const newMessage = formatMessage(message)
        messageList.value.push(newMessage)
        scrollToBottom()
      }
    }
  })
}

// 消息已读回执
const onMessageReadByPeer = (event: any) => {
  // 更新消息的已读状态
  const messages = event.data
  messages.forEach((readMessage: any) => {
    const message = messageList.value.find((msg) => msg.id === readMessage.ID)
    if (message) {
      message.isRead = true
    }
  })
}

// 标记消息为已读
const markMessagesAsRead = async () => {
  try {
    // 使用腾讯云IM SDK标记消息已读
    if (imManager.isReady && conversationID.value) {
      await imManager.markMessageAsRead(conversationID.value)
    }
  } catch (error) {
    console.error('标记消息已读失败:', error)
  }
}

// 滚动到底部
const scrollToBottom = () => {
  nextTick(() => {
    // 使用更大的值确保滚动到底部
    scrollTop.value = 99999999
  })
}

// 输入框事件
const onInputFocus = () => {
  setTimeout(() => {
    scrollToBottom()
  }, 300)
}

const onInputBlur = () => {
  // 输入框失焦处理
}

// 格式化时间
const formatTime = (timestamp: number): string => {
  const date = new Date(timestamp)
  const now = new Date()

  if (date.toDateString() === now.toDateString()) {
    return date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })
  } else {
    return date.toLocaleDateString('zh-CN', { month: '2-digit', day: '2-digit' })
  }
}

// 是否显示时间
const shouldShowTime = (currentMessage: Message, previousMessage?: Message): boolean => {
  if (!previousMessage) return true
  return currentMessage.timestamp - previousMessage.timestamp > 5 * 60 * 1000 // 5分钟
}

// 切换语音模式
const toggleVoiceMode = () => {
  isVoiceMode.value = !isVoiceMode.value
  showEmojiPanel.value = false
  showMorePanel.value = false
  showQuickReplyPanel.value = false
}

// 开始语音录制
const startVoiceRecord = () => {
  isRecording.value = true
  // TODO: 实现语音录制功能
  console.log('开始录音')
}

// 结束语音录制
const endVoiceRecord = () => {
  isRecording.value = false
  // TODO: 实现语音录制结束和发送功能
  console.log('结束录音')
}

// 切换表情面板
const toggleEmojiPanel = () => {
  showEmojiPanel.value = !showEmojiPanel.value
  showMorePanel.value = false
  showQuickReplyPanel.value = false
  isVoiceMode.value = false
}

// 切换更多功能面板
const toggleMorePanel = () => {
  showMorePanel.value = !showMorePanel.value
  showEmojiPanel.value = false
  showQuickReplyPanel.value = false
  isVoiceMode.value = false
}

// 切换常用语面板
const toggleQuickReplyPanel = () => {
  showQuickReplyPanel.value = !showQuickReplyPanel.value
  showEmojiPanel.value = false
  showMorePanel.value = false
  isVoiceMode.value = false
}

// 插入表情
const insertEmoji = (emoji: string) => {
  inputText.value += emoji
}

// 选择常用语
const selectQuickReply = (reply: string) => {
  inputText.value = reply
  showQuickReplyPanel.value = false
}

// 选择图片
const selectImage = () => {
  uni.chooseImage({
    count: 9,
    sizeType: ['original', 'compressed'],
    sourceType: ['album', 'camera'],
    success: () => {
      // TODO: 实现图片发送功能
      showMorePanel.value = false
      uni.showToast({
        title: '图片发送功能开发中',
        icon: 'none',
      })
    },
  })
}

// 选择文件
const selectFile = () => {
  // TODO: 实现文件选择功能
  showMorePanel.value = false
  uni.showToast({
    title: '文件发送功能开发中',
    icon: 'none',
  })
}

// 选择位置
const selectLocation = () => {
  uni.chooseLocation({
    success: () => {
      // TODO: 实现位置发送功能
      showMorePanel.value = false
      uni.showToast({
        title: '位置发送功能开发中',
        icon: 'none',
      })
    },
  })
}

// 选择名片
const selectContact = () => {
  // TODO: 实现名片选择功能
  showMorePanel.value = false
  uni.showToast({
    title: '名片发送功能开发中',
    icon: 'none',
  })
}

// 获取已读状态文本
const getReadStatusText = (message: Message): string => {
  if (message.isRead === undefined) return ''

  if (chatInfo.type === 'GROUP') {
    return message.readCount ? `${message.readCount}人已读` : '未读'
  } else {
    return message.isRead ? '已读' : '未读'
  }
}

// 隐藏未读提醒弹窗
const hideUnreadAlert = () => {
  showUnreadAlert.value = false
}

// 拨打电话
const makePhoneCall = () => {
  hideUnreadAlert()

  // 获取对方的电话号码（这里需要根据实际情况获取）
  const phoneNumber = getContactPhoneNumber()

  if (phoneNumber) {
    uni.makePhoneCall({
      phoneNumber: phoneNumber,
      success: () => {},
      fail: (error) => {
        console.error('拨打电话失败:', error)
        uni.showToast({
          title: '拨打电话失败',
          icon: 'error',
        })
      },
    })
  } else {
    uni.showToast({
      title: '未找到联系方式',
      icon: 'none',
    })
  }
}

// 获取联系人电话号码
const getContactPhoneNumber = (): string => {
  // TODO: 根据实际业务逻辑获取联系人电话号码
  // 这里可以从用户资料、好友信息或其他数据源获取

  // 示例：从聊天信息中获取（需要根据实际数据结构调整）
  if (chatInfo.type === 'C2C') {
    // 可以从用户资料中获取电话号码
    // return chatInfo.phoneNumber || ''

    // 临时示例号码（实际使用时需要替换为真实逻辑）
    return '13800138000'
  }

  return ''
}

// 清除未读提醒计时器
const clearUnreadTimer = () => {
  if (unreadTimer.value) {
    clearTimeout(unreadTimer.value)
    unreadTimer.value = null
  }
  unreadCount.value = 0
}

// 跳转到设置页面
const goToSettings = () => {
  if (chatInfo.type === 'GROUP') {
    // 群聊设置 - 跳转到群聊信息页面
    uni.navigateTo({
      url: `/pages/chat/group-info?groupId=${chatInfo.id}&groupName=${encodeURIComponent(
        chatInfo.name
      )}`,
    })
  } else {
    // 私聊设置 - 跳转到聊天设置页面
    uni.navigateTo({
      url: `/pages/chat/settings?userId=${chatInfo.id}&username=${encodeURIComponent(
        chatInfo.name
      )}`,
    })
  }
}

// 开发测试函数
const testPersonalChat = () => {
  showMorePanel.value = false
  uni.navigateTo({
    url: `/pages/chat/settings?userId=administrator&username=${encodeURIComponent('管理员')}`,
  })
}

const testGroupChat = () => {
  showMorePanel.value = false
  uni.navigateTo({
    url:
      '/pages/chat/group-info?groupId=test_group_123&groupName=' + encodeURIComponent('测试群聊'),
  })
}
</script>

<style scoped lang="scss">
.chat-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
}

.message-list {
  flex: 1;
  // padding: 20rpx;
  overflow-y: auto;
}

.loading-more {
  text-align: center;
  padding: 20rpx;
}

.loading-text {
  font-size: 24rpx;
  color: #999;
}

.message-item {
  margin-bottom: 30rpx;
}

.time-divider {
  text-align: center;
  margin: 20rpx 0;
}

.time-text {
  font-size: 22rpx;
  color: #999;
  background-color: rgba(0, 0, 0, 0.1);
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
}

.message-wrapper {
  width: 100%;
}

.message-row {
  display: flex;
  align-items: flex-start;
  flex-direction: row;

  &.own-row {
    justify-content: flex-end;
  }
}

.avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 40rpx;
  overflow: hidden;
  margin: 0 20rpx;
}

.avatar-img {
  width: 100%;
  height: 100%;
}

.message-content {
  max-width: 500rpx;

  &.own-content {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
  }
}

.sender-name {
  margin-bottom: 8rpx;
}

.name-text {
  font-size: 22rpx;
  color: #666;
}

.message-bubble {
  padding: 20rpx;
  border-radius: 20rpx;
  max-width: 100%;
  word-wrap: break-word;

  &.other-bubble {
    background-color: white;
    border-top-left-radius: 8rpx;
  }

  &.own-bubble {
    background-color: #1989fa;
    border-top-right-radius: 8rpx;

    .message-text {
      color: white;
    }
  }
}

.message-text {
  font-size: 28rpx;
  line-height: 1.4;
  color: #333;
}

.read-status {
  margin-top: 8rpx;
}

.read-text {
  font-size: 20rpx;
  color: #999;
}

.input-area {
  background-color: white;
  border-top: 1rpx solid #e0e0e0;
  padding: 20rpx;
}

.input-container {
  display: flex;
  align-items: center;
  gap: 16rpx;
  flex-direction: row;
}

.voice-btn,
.emoji-btn,
.more-btn {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background-color: #f8f8f8;
  transition: background-color 0.2s;
}

.voice-btn:active,
.emoji-btn:active,
.more-btn:active {
  background-color: #e8e8e8;
}

.btn-icon {
  width: 48rpx;
  height: 48rpx;
}

.input-wrapper {
  flex: 1;
}

.text-input-container {
  display: flex;
  align-items: center;
  width: 100%;
  border: 1rpx solid #e0e0e0;
  border-radius: 25rpx;
  background-color: #f8f8f8;
  overflow: hidden;
}

.message-input {
  flex: 1;
  padding: 20rpx 24rpx;
  border: none;
  background: transparent;
  font-size: 28rpx;
  line-height: 1.4;
}

.quick-reply-btn {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #e8e8e8;
  border-radius: 50%;
  margin-right: 12rpx;
  transition: background-color 0.2s;
}

.quick-reply-btn:active {
  background-color: #d8d8d8;
}

.quick-reply-text {
  font-size: 24rpx;
  color: #666;
  font-weight: bold;
}

.voice-input-btn {
  width: 100%;
  padding: 20rpx 24rpx;
  border: 1rpx solid #e0e0e0;
  border-radius: 25rpx;
  background-color: #f8f8f8;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 80rpx;
}

.voice-input-text {
  font-size: 28rpx;
  color: #666;
}

.send-btn {
  padding: 20rpx 30rpx;
  background-color: #1989fa;
  border-radius: 25rpx;
  min-width: 100rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.send-text {
  color: white;
  font-size: 28rpx;
}

/* 表情面板样式 */
.emoji-panel {
  margin-top: 20rpx;
  background-color: #f8f8f8;
  border-radius: 16rpx;
  padding: 20rpx;
}

.emoji-grid {
  display: grid !important;
  grid-template-columns: repeat(8, 1fr);
  gap: 16rpx;
}

.emoji-item {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 12rpx;
  background-color: white;
  transition: background-color 0.2s;
}

.emoji-item:active {
  background-color: #e8e8e8;
}

.emoji-text {
  font-size: 32rpx;
}

/* 常用语面板样式 */
.quick-reply-panel {
  margin-top: 20rpx;
  background-color: #f8f8f8;
  border-radius: 16rpx;
  padding: 20rpx;
}

.quick-reply-grid {
  display: grid !important;
  grid-template-columns: repeat(2, 1fr);
  gap: 16rpx;
}

.quick-reply-item {
  padding: 20rpx 24rpx;
  background-color: white;
  border-radius: 25rpx;
  border: 1rpx solid #e0e0e0;
  transition: background-color 0.2s;
  min-height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.quick-reply-item:active {
  background-color: #e8e8e8;
}

.quick-reply-item-text {
  font-size: 28rpx;
  color: #333;
  text-align: center;
  line-height: 1.4;
}

/* 更多功能面板样式 */
.more-panel {
  margin-top: 20rpx;
  background-color: #f8f8f8;
  border-radius: 16rpx;
  padding: 20rpx;
}

.more-grid {
  display: grid !important;
  grid-template-columns: repeat(4, 1fr);
  gap: 32rpx;
}

.more-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12rpx;
  padding: 20rpx;
  border-radius: 12rpx;
  background-color: white;
  transition: background-color 0.2s;
}

.more-item:active {
  background-color: #e8e8e8;
}

.more-icon {
  width: 80rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 16rpx;
  background-color: #f0f0f0;
}

.more-icon-img {
  width: 48rpx;
  height: 48rpx;
}

.more-icon-text {
  font-size: 36rpx;
  line-height: 1;
}

.more-text {
  font-size: 24rpx;
  color: #666;
}

/* 未读消息提醒弹窗样式 */
.unread-alert-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 2000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.unread-alert {
  width: 600rpx;
  background-color: white;
  border-radius: 24rpx;
  overflow: hidden;
  animation: alertSlideIn 0.3s ease-out;
}

@keyframes alertSlideIn {
  from {
    transform: scale(0.8);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

.alert-content {
  padding: 60rpx 40rpx 40rpx;
  text-align: center;
}

.alert-title {
  display: block;
  font-size: 36rpx;
  color: #333;
  font-weight: bold;
  margin-bottom: 20rpx;
}

.alert-message {
  display: block;
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
  margin-bottom: 60rpx;
}

.alert-actions {
  display: flex;
  gap: 20rpx;
}

.alert-btn {
  flex: 1;
  padding: 32rpx;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
}

.cancel-btn {
  background-color: #f8f8f8;
  border: 1rpx solid #e0e0e0;
}

.cancel-btn .btn-text {
  color: #666;
}

.call-btn {
  background-color: #1989fa;
}

.call-btn .btn-text {
  color: white;
}

.alert-btn:active {
  transform: scale(0.95);
}

.btn-text {
  font-size: 32rpx;
  font-weight: bold;
}
</style>
