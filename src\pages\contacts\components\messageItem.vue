<template>
  <view class="flex-row items-center p-[10rpx] mb-[20rpx] bg-white">
    <!-- 头像 - 点击跳转到用户详情 -->
    <view
      class="avatar-cell w-[100rpx] h-[100rpx] rounded-[50rpx] overflow-hidden bg-light mr-[20rpx]"
      @click="openUserProfile"
    >
      <image
        :src="props.data.avatar || '/static/images/user/default_avatar.png'"
        class="w-[100%] h-[100%]"
      ></image>
    </view>

    <!-- 内容区域 - 点击进入聊天 -->
    <view class="content flex-1 overflow-hidden" @click="openChat">
      <!-- 用户名和时间 -->
      <view class="flex-row items-center justify-between mb-[10rpx]">
        <text class="text-lg font-medium">{{ props.data.username }}</text>
        <text class="text-[24rpx] text-muted">{{ props.data.time }}</text>
      </view>

      <!-- 最后一条消息 -->
      <view class="flex-row items-center justify-between">
        <view class="message flex-1 mr-[10rpx]">
          <text class="text-[28rpx] text-content line-clamp-1">{{ props.data.lastMessage }}</text>
        </view>

        <!-- 未读消息数量 -->
        <view
          v-if="props.data.unreadCount > 0"
          class="badge bg-red-500 text-white text-[20rpx] px-[8rpx] py-[2rpx] rounded-[12rpx] min-w-[24rpx] text-center"
        >
          {{ props.data.unreadCount > 99 ? '99+' : props.data.unreadCount }}
        </view>
      </view>
    </view>

    <!-- 更多操作 -->
    <view class="ml-[12rpx]" @click="openUserProfile">
      <c-icon type="gengduo" color="#999999"></c-icon>
    </view>
  </view>
</template>

<script setup lang="ts">
import { type PropType } from 'vue'

interface IMessage {
  id: string
  avatar: string
  username: string
  lastMessage: string
  time: string
  unreadCount: number
  isGroup?: boolean // 是否为群聊
  groupId?: string // 群聊ID
}

const props = defineProps({
  data: {
    type: Object as PropType<IMessage>,
    default: () => ({}),
  },
})

const openChat = () => {
  // 跳转到聊天页面
  if (props.data.isGroup) {
    // 群聊
    uni.navigateTo({
      url: `/pages/chat/chat?groupId=${props.data.groupId}&groupName=${encodeURIComponent(
        props.data.username
      )}&isGroup=true`,
    })
  } else {
    // 个人聊天
    uni.navigateTo({
      url: `/pages/chat/chat?userId=${props.data.id}&username=${encodeURIComponent(
        props.data.username
      )}`,
    })
  }
}

const openUserProfile = () => {
  // 跳转到用户详情页面
  if (props.data.isGroup) {
    // 群聊 - 跳转到群聊信息页面
    uni.navigateTo({
      url: `/pages/chat/group-info?groupId=${props.data.groupId}&groupName=${encodeURIComponent(
        props.data.username
      )}`,
    })
  } else {
    // 个人用户 - 跳转到用户详情页面
    uni.navigateTo({
      url: `/pages/user/profile?userId=${props.data.id}&username=${encodeURIComponent(
        props.data.username
      )}`,
    })
  }
}
</script>

<style scoped lang="scss">
.badge {
  height: 26rpx;
  border-radius: 13rpx;
  min-width: 36rpx;
  padding-top: 4rpx;
  padding-bottom: 4rpx;
}

.message {
  text {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}

.line-clamp-1 {
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
