/**
 * IM配置文件 - 包含测试用户数据和配置
 */

// 腾讯云IM配置
export const IM_CONFIG = {
  // 腾讯云IM应用的SDKAppID
  SDKAppID: 1600101880, // 请替换为你的SDKAppID

  // 日志级别
  LOG_LEVEL: {
    DEBUG: 0, // 普通级别，日志量较多，接入时建议使用
    RELEASE: 1, // release级别，SDK 输出关键信息，生产环境时建议使用
    WARN: 2, // 告警级别，SDK 只输出告警和错误级别的日志
    ERROR: 3, // 错误级别，SDK 只输出错误级别的日志
    NONE: 4, // 无日志级别，SDK 将不打印任何日志
  },
}

// IM事件类型
export const IM_EVENTS = {
  // SDK就绪
  SDK_READY: 'SDK_READY',
  SDK_NOT_READY: 'SDK_NOT_READY',

  // 消息相关
  MESSAGE_RECEIVED: 'MESSAGE_RECEIVED',
  MESSAGE_MODIFIED: 'MESSAGE_MODIFIED',
  MESSAGE_REVOKED: 'MESSAGE_REVOKED',
  MESSAGE_READ_BY_PEER: 'MESSAGE_READ_BY_PEER',

  // 会话相关
  CONVERSATION_LIST_UPDATED: 'CONVERSATION_LIST_UPDATED',
  TOTAL_UNREAD_MESSAGE_COUNT_UPDATED: 'TOTAL_UNREAD_MESSAGE_COUNT_UPDATED',

  // 群组相关
  GROUP_LIST_UPDATED: 'GROUP_LIST_UPDATED',

  // 用户相关
  PROFILE_UPDATED: 'PROFILE_UPDATED',
  BLACKLIST_UPDATED: 'BLACKLIST_UPDATED',

  // 网络状态
  NET_STATE_CHANGE: 'NET_STATE_CHANGE',

  // 被踢下线
  KICKED_OUT: 'KICKED_OUT',
}

// 消息类型
export const MESSAGE_TYPES = {
  TEXT: 'TIM.TYPES.MSG_TEXT',
  IMAGE: 'TIM.TYPES.MSG_IMAGE',
  AUDIO: 'TIM.TYPES.MSG_AUDIO',
  VIDEO: 'TIM.TYPES.MSG_VIDEO',
  FILE: 'TIM.TYPES.MSG_FILE',
  FACE: 'TIM.TYPES.MSG_FACE',
  LOCATION: 'TIM.TYPES.MSG_LOCATION',
  CUSTOM: 'TIM.TYPES.MSG_CUSTOM',
}

// 会话类型
export const CONVERSATION_TYPES = {
  C2C: 'TIM.TYPES.CONV_C2C', // 单聊
  GROUP: 'TIM.TYPES.CONV_GROUP', // 群聊
}

// 网络状态
export const NET_STATE = {
  CONNECTED: 'TIM.TYPES.NET_STATE_CONNECTED',
  CONNECTING: 'TIM.TYPES.NET_STATE_CONNECTING',
  DISCONNECTED: 'TIM.TYPES.NET_STATE_DISCONNECTED',
}

// 测试用户接口定义
export interface TestUser {
  userID: string
  username: string
  nickname: string
  phone: string
  avatar?: string
  userSig: string
}

// 真实用户数据 - 包含3个真实用户数据
const TEST_USERS: TestUser[] = [
  {
    userID: '1960535231902826498',
    username: 'user1',
    nickname: '测试账号一',
    phone: '15263215021',
    avatar: undefined,
    userSig:
      'eJw1jssOgjAURP*lWw25LbS2JO5cqCFGKSx0h6U2N-hoEJFo-HcJ4nIeZzJvkiU6sJ3H2pKYUxkBwHQwW1uTmLAAyE-fy6rwHksSUwFAgUo5NrG01wZPOABUCeAhZyFVwCQTkZL-AXR9bsxeQ5Ke9XaV5lnq1vZ2KCo98cdl9zKLTSueOTr92Hk3H8EGL-05OuOCyTBS-PMFLaoz-g__',
  },
  {
    userID: '1960535362584756226',
    username: 'user2',
    nickname: '测试账号二',
    phone: '15263215022',
    avatar: undefined,
    userSig:
      'eJw1js0KgkAUhd9ltobcOzrXSWgjtbI2ZYHuhJniGsYwikjRu2dmy-PzHc5LFPtTaEfH3opUoY4BYDWbg-UiFTIE8dOdudfOsREpEgACar002dhHz1eeAVwTqEhFJJWOE0VS0n*Ab1NeXgafj90xoL5xZZFn9pz5pmrNrtoSFm3tsq7BwDz9YbOAPbfTOfyOaRkl8P4AMDgzrw__',
  },
  {
    userID: '1960545000583643138',
    username: 'user3',
    nickname: '测试333',
    phone: '13333333333',
    avatar: '83a9e58a30e846fd8bcdd980b1a88e5c.png',
    userSig:
      'eJw1jssKgkAUht9ltoWd41ychBZR4EJdlRnRRnOUaUoGEwuid28wW-63j-9N9snOUy*rO0VCjpIBwHw0B9WRkPgekJ9*VKawVlckRAGAgFJOTV2ptte1Hge4FMAZdxguqWAUqfwDdOPyIFqn54XIs7IuzTU*9MmsaDbDNjVIY9Pe8HiyLIueWV5fVtOy13f3DgMufHeQi88XLCYzKQ__',
  },
]

/**
 * 获取所有测试用户
 */
export const getAllTestUsers = (): TestUser[] => {
  return TEST_USERS
}

/**
 * 根据用户ID获取测试用户
 */
export const getTestUserById = (userID: string): TestUser | undefined => {
  return TEST_USERS.find((user) => user.userID === userID)
}

/**
 * 模拟好友申请数据
 */
export const getMockFriendRequests = () => {
  return [
    {
      userID: '1960535362584756226',
      nickname: '测试账号二',
      avatar: '/static/images/user/default_avatar.png',
      message: '我是测试账号二，想和您成为好友',
      time: '2025-08-27 10:50:30',
    },
    {
      userID: '1960545000583643138',
      nickname: '测试333',
      avatar: '/static/images/user/83a9e58a30e846fd8bcdd980b1a88e5c.png',
      message: '您好，我是测试333',
      time: '2025-08-27 11:28:48',
    },
  ]
}
