/**
 * IM配置文件 - 包含测试用户数据和配置
 */

// 腾讯云IM配置
export const IM_CONFIG = {
  // 腾讯云IM应用的SDKAppID
  SDKAppID: 1600101880, // 请替换为你的SDKAppID

  // 日志级别
  LOG_LEVEL: {
    DEBUG: 0, // 普通级别，日志量较多，接入时建议使用
    RELEASE: 1, // release级别，SDK 输出关键信息，生产环境时建议使用
    WARN: 2, // 告警级别，SDK 只输出告警和错误级别的日志
    ERROR: 3, // 错误级别，SDK 只输出错误级别的日志
    NONE: 4, // 无日志级别，SDK 将不打印任何日志
  },
}

// IM事件类型
export const IM_EVENTS = {
  // SDK就绪
  SDK_READY: 'SDK_READY',
  SDK_NOT_READY: 'SDK_NOT_READY',

  // 消息相关
  MESSAGE_RECEIVED: 'MESSAGE_RECEIVED',
  MESSAGE_MODIFIED: 'MESSAGE_MODIFIED',
  MESSAGE_REVOKED: 'MESSAGE_REVOKED',
  MESSAGE_READ_BY_PEER: 'MESSAGE_READ_BY_PEER',

  // 会话相关
  CONVERSATION_LIST_UPDATED: 'CONVERSATION_LIST_UPDATED',
  TOTAL_UNREAD_MESSAGE_COUNT_UPDATED: 'TOTAL_UNREAD_MESSAGE_COUNT_UPDATED',

  // 群组相关
  GROUP_LIST_UPDATED: 'GROUP_LIST_UPDATED',

  // 用户相关
  PROFILE_UPDATED: 'PROFILE_UPDATED',
  BLACKLIST_UPDATED: 'BLACKLIST_UPDATED',

  // 网络状态
  NET_STATE_CHANGE: 'NET_STATE_CHANGE',

  // 被踢下线
  KICKED_OUT: 'KICKED_OUT',
}

// 消息类型
export const MESSAGE_TYPES = {
  TEXT: 'TIM.TYPES.MSG_TEXT',
  IMAGE: 'TIM.TYPES.MSG_IMAGE',
  AUDIO: 'TIM.TYPES.MSG_AUDIO',
  VIDEO: 'TIM.TYPES.MSG_VIDEO',
  FILE: 'TIM.TYPES.MSG_FILE',
  FACE: 'TIM.TYPES.MSG_FACE',
  LOCATION: 'TIM.TYPES.MSG_LOCATION',
  CUSTOM: 'TIM.TYPES.MSG_CUSTOM',
}

// 会话类型
export const CONVERSATION_TYPES = {
  C2C: 'TIM.TYPES.CONV_C2C', // 单聊
  GROUP: 'TIM.TYPES.CONV_GROUP', // 群聊
}

// 网络状态
export const NET_STATE = {
  CONNECTED: 'TIM.TYPES.NET_STATE_CONNECTED',
  CONNECTING: 'TIM.TYPES.NET_STATE_CONNECTING',
  DISCONNECTED: 'TIM.TYPES.NET_STATE_DISCONNECTED',
}

// 测试用户接口定义
export interface TestUser {
  userID: string
  username: string
  nickname: string
  phone: string
  avatar?: string
  userSig: string
}

// 测试用户数据 - 包含3个真实用户数据
const TEST_USERS: TestUser[] = [
  {
    userID: 'administrator',
    username: '管理员',
    nickname: '管理员',
    phone: '13800000000',
    avatar: 'tx.png',
    userSig:
      'eJwtzNEKgjAYBeB32W0h-7TZErwMAgiUhJ0R3s636M3VsQ4To3TPz8nzncN5E5GUwaEsSEgZA1nNGpTuPN5xZqhY7dN5K39tl4FQjjUFFEhoDUKCcw7-Ro0GrJ2eMhQCLemx-tmUxBYj4bnnB*-SfiaMyFYi6zorXUwlZNpRfxtGds7yKVtfO9XtWnIbHYZOSzxdePzUQ',
  },
  {
    userID: '19573461517477745',
    username: '牛牛1',
    nickname: '牛牛1',
    phone: '13800000001',
    avatar: 'tx.png',
    userSig:
      'eJwtzUELgkAQBeD-sueQWdvZHYWOBVZ6qMCuwa4xa8aiJkX03zPzON97vHmL0-4YDa4VqYgjEIvpZuvuPVc8sUzQLJWWKI0yxiicS52tLyGwFanUABIkEfwT9wzcutERMQaYtefmZwa1BFBI8wpfxx9rf65wubWdLup84x4uaYKl28HvspIwf2UWgy-KgTytxOcLdtIy2g__',
  },
  {
    userID: '19573462910530810',
    username: '贝贝1',
    nickname: '贝贝1',
    phone: '13800000002',
    avatar: 'tx.png',
    userSig:
      'eJw1zc0KgkAUBeB3mXXEuaOjo9CijUUI0Z-VMppRLtFgaiFF756ZLe93Due*xDbdjB-2ErGQY4hRf7OxruGce6ZIhZ4fyIigPGj6l2pzOZUlGxFTABBIa-wS25Zc2c6VUhIYtOHr10IVEKCkHla46H64JM*OqckPs*VinXjPKl1ltW39WxFp8HzqWuDudtneP0-E*wNyljLX',
  },
]

/**
 * 获取所有测试用户
 */
export const getAllTestUsers = (): TestUser[] => {
  return TEST_USERS
}

/**
 * 根据用户ID获取测试用户
 */
export const getTestUserById = (userID: string): TestUser | undefined => {
  return TEST_USERS.find((user) => user.userID === userID)
}

/**
 * 模拟好友申请数据
 */
export const getMockFriendRequests = () => {
  return [
    {
      userID: '19573461517477745',
      nickname: '牛牛1',
      avatar: '/static/images/user/tx.png',
      message: '我是牛牛1，想和您成为好友',
      time: '2024-01-15 10:30:00',
    },
    {
      userID: '19573462910530810',
      nickname: '贝贝1',
      avatar: '/static/images/user/tx.png',
      message: '您好，我是贝贝1',
      time: '2024-01-15 11:20:00',
    },
  ]
}
