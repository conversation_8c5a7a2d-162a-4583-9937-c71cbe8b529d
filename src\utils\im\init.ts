/**
 * IM初始化工具
 * 用于在应用启动时初始化IM SDK
 */

import imManager, { type Message } from './index'
import { IM_CONFIG } from '@/config/im'
import { useUserStore } from '@/stores/user'

/**
 * 初始化IM SDK
 */
export async function initIM(): Promise<boolean> {
  try {
    const userStore = useUserStore()

    // 检查用户是否已登录
    if (!userStore.isLogin) {
      console.log('用户未登录，跳过IM初始化')
      return false
    }

    const userId = userStore.userInfo?.userId || 'test_user'

    // 初始化IM SDK
    const initSuccess = await imManager.init()

    if (!initSuccess) {
      console.error('IM SDK初始化失败')
      return false
    }

    // 检查是否有imSign
    if (!userStore.userInfo.imSign) {
      console.error('用户信息中没有imSign字段，无法登录IM')
      return false
    }

    // 使用自动登录方法
    try {
      const loginSuccess = await imManager.autoLogin()

      if (loginSuccess) {
        console.log('IM登录成功')
        return true
      } else {
        console.error('IM登录失败')
        return false
      }
    } catch (error) {
      console.error('IM登录失败:', error)
      return false
    }
  } catch (error) {
    console.error('IM初始化异常:', error)
    return false
  }
}

/**
 * 登出IM
 */
export async function logoutIM(): Promise<boolean> {
  try {
    const success = await imManager.logout()
    if (success) {
      console.log('IM登出成功')
    } else {
      console.error('IM登出失败')
    }
    return success
  } catch (error) {
    console.error('IM登出异常:', error)
    return false
  }
}

/**
 * 检查IM连接状态
 */
export function checkIMStatus(): boolean {
  return imManager.isReady
}

/**
 * 重连IM
 */
export async function reconnectIM(): Promise<boolean> {
  try {
    const userStore = useUserStore()

    if (!userStore.isLogin) {
      console.log('用户未登录，无法重连IM')
      return false
    }

    // 先登出
    await logoutIM()

    // 重新初始化并登录
    return await initIM()
  } catch (error) {
    console.error('IM重连异常:', error)
    return false
  }
}

/**
 * 获取IM实例
 */
export function getIMInstance() {
  return imManager
}

/**
 * 设置IM事件监听器
 */
export function setupIMListeners() {
  // 可以在这里设置全局的IM事件监听器
  // 比如网络状态变化、被踢下线等

  imManager.addMessageListener((message: Message) => {
    // 全局消息处理
    console.log('收到新消息:', message)

    // 可以在这里处理消息通知、声音提醒等
    handleNewMessage(message)
  })
}

/**
 * 处理新消息
 */
function handleNewMessage(message: any) {
  // 消息通知处理
  if (message.from !== imManager.sdk?.getMyUserID()) {
    // 显示消息通知
    showMessageNotification(message)

    // 播放提示音
    playNotificationSound()

    // 更新未读消息数
    updateUnreadCount()
  }
}

/**
 * 显示消息通知
 */
function showMessageNotification(message: any) {
  // #ifdef H5
  if ('Notification' in window && Notification.permission === 'granted') {
    const notification = new Notification('新消息', {
      body: message.payload?.text || '收到一条新消息',
      icon: '/static/icons/message.png',
      tag: message.conversationID, // 防止重复通知
      requireInteraction: false,
      silent: false,
    })

    // 点击通知跳转到聊天页面
    notification.onclick = () => {
      window.focus()
      // 跳转到对应的聊天页面
      const isGroup = message.conversationType === 'GROUP'
      const targetId = isGroup ? message.to : message.from
      const targetName = message.nick || targetId

      const url = isGroup
        ? `#/pages/chat/chat?groupId=${targetId}&groupName=${targetName}`
        : `#/pages/chat/chat?userId=${targetId}&username=${targetName}`

      window.location.hash = url
      notification.close()
    }

    // 自动关闭通知
    setTimeout(() => {
      notification.close()
    }, 5000)
  }
  // #endif

  // #ifdef APP-PLUS
  // 使用uni-app的本地通知
  plus.push.createMessage(
    message.payload?.text || '收到一条新消息',
    JSON.stringify({
      type: 'chat',
      conversationID: message.conversationID,
      conversationType: message.conversationType,
      from: message.from,
      to: message.to,
    }),
    {
      title: '新消息',
      when: new Date(),
      icon: '/static/icons/message.png',
    }
  )
  // #endif

  // #ifdef MP-WEIXIN
  // 小程序暂时只能显示toast提示
  uni.showToast({
    title: '收到新消息',
    icon: 'none',
    duration: 2000,
  })
  // #endif
}

/**
 * 播放提示音
 */
function playNotificationSound() {
  // #ifdef H5
  try {
    const audio = new Audio('/static/sounds/message.mp3')
    audio.play().catch((e) => {
      console.log('播放提示音失败:', e)
    })
  } catch (error) {
    console.log('播放提示音异常:', error)
  }
  // #endif

  // #ifdef APP-PLUS
  // 使用uni-app的音频API
  uni.createInnerAudioContext().play()
  // #endif
}

/**
 * 更新未读消息数
 */
function updateUnreadCount() {
  // 获取会话列表并计算总未读数
  imManager.getConversationList().then((conversations) => {
    const totalUnread = conversations.reduce((total, conv) => total + conv.unreadCount, 0)

    // 更新应用角标
    // #ifdef APP-PLUS
    plus.runtime.setBadgeNumber(totalUnread)
    // #endif

    // 更新tabbar角标
    if (totalUnread > 0) {
      uni.setTabBarBadge({
        index: 2, // 消息tab的索引
        text: totalUnread > 99 ? '99+' : totalUnread.toString(),
      })
    } else {
      uni.removeTabBarBadge({
        index: 2,
      })
    }
  })
}

/**
 * 请求通知权限
 */
export function requestNotificationPermission() {
  // #ifdef H5
  if ('Notification' in window && Notification.permission === 'default') {
    Notification.requestPermission().then((permission) => {
      console.log('通知权限:', permission)
    })
  }
  // #endif
}

/**
 * IM状态枚举
 */
export enum IMStatus {
  NOT_INITIALIZED = 'not_initialized',
  INITIALIZING = 'initializing',
  INITIALIZED = 'initialized',
  LOGGING_IN = 'logging_in',
  LOGGED_IN = 'logged_in',
  LOGGING_OUT = 'logging_out',
  LOGGED_OUT = 'logged_out',
  ERROR = 'error',
}

/**
 * IM状态管理
 */
class IMStatusManager {
  private status: IMStatus = IMStatus.NOT_INITIALIZED
  private listeners: Array<(status: IMStatus) => void> = []

  setStatus(status: IMStatus) {
    this.status = status
    this.listeners.forEach((listener) => listener(status))
  }

  getStatus(): IMStatus {
    return this.status
  }

  addStatusListener(listener: (status: IMStatus) => void) {
    this.listeners.push(listener)
  }

  removeStatusListener(listener: (status: IMStatus) => void) {
    const index = this.listeners.indexOf(listener)
    if (index > -1) {
      this.listeners.splice(index, 1)
    }
  }
}

export const imStatusManager = new IMStatusManager()

export default {
  initIM,
  logoutIM,
  checkIMStatus,
  reconnectIM,
  getIMInstance,
  setupIMListeners,
  requestNotificationPermission,
  imStatusManager,
  IMStatus,
}
