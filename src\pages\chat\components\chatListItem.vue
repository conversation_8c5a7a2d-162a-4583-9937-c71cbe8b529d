<template>
  <view class="flex-row items-center p-[10rpx] mb-[20rpx] bg-white" @click="openChat">
    <view
      class="avatar-cell w-[100rpx] h-[100rpx] rounded-[50rpx] overflow-hidden bg-light mr-[20rpx]"
    >
      <image
        :src="props.data.avatar || '/statics/user/default_avatar.png'"
        class="w-[100%] h-[100%]"
      ></image>
    </view>
    <view class="content flex-1 overflow-hidden">
      <view class="title mb-[20rpx]">
        <text class="text-lg">{{ props.data.title }}</text>
      </view>
      <view class="message">
        <text class="text-sm text-secondary">{{ props.data.message }}</text>
      </view>
    </view>
    <view class="items-end ml-[12rpx]">
      <view class="time mb-[20rpx]">
        <text class="text-sm text-secondary">{{ dayjs(props.data.time).format('HH:mm') }}</text>
      </view>
      <view class="badge flex-row items-center justify-center bg-highlight">
        <text class="text-mn text-white">{{ props.data.unready }}</text>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import dayjs from 'dayjs'
import type { IChatListItem } from 'types/chat.interface'
import { type PropType } from 'vue'
const props = defineProps({
  data: {
    type: Object as PropType<IChatListItem>,
    default: () => ({}),
  },
})

// 打开聊天页面
const openChat = () => {
  // 跳转到聊天页面
  uni.navigateTo({
    url: `/pages/chat/chat?userID=${props.data.id}&username=${encodeURIComponent(
      props.data.title
    )}`,
  })
}
</script>

<style scoped lang="scss">
.badge {
  height: 26rpx;
  border-radius: 13rpx;
  min-width: 36rpx;
  padding-top: 4rpx;
  padding-bottom: 4rpx;
}
.message {
  text {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}
</style>
