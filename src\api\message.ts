/**
 * 消息管理API
 */
import request from '@/utils/request'

export interface Message {
  id: string
  conversationId: string
  senderId: string
  senderName: string
  senderAvatar?: string
  receiverId?: string
  receiverType: 'user' | 'group'
  messageType: 'text' | 'image' | 'voice' | 'video' | 'file' | 'location' | 'custom'
  content: string
  extra?: any // 额外数据，如文件信息、位置信息等
  status: 'sending' | 'sent' | 'delivered' | 'read' | 'failed'
  isRecalled: boolean
  recallTime?: string
  recallReason?: string
  replyToMessageId?: string
  replyToMessage?: Partial<Message>
  createTime: string
  updateTime?: string
  readTime?: string
  deliveredTime?: string
  isDeleted: boolean
  seq: number // 消息序列号
  random: number // 消息随机数
}

export interface MessageResponse {
  code: number
  message: string
  data: Message
}

export interface MessageListResponse {
  code: number
  message: string
  data: Message[]
  total?: number
  hasMore?: boolean
  nextSeq?: number
}

export interface MessagePageParams {
  current?: number
  size?: number
  conversationId?: string
  senderId?: string
  receiverId?: string
  messageType?: string
  status?: string
  startTime?: string
  endTime?: string
  keyword?: string
  beforeSeq?: number
  afterSeq?: number
}

/**
 * 修改消息
 */
export const updateMessage = (messageId: string, data: Partial<Message>) => {
  return request.put<{ code: number; message: string }>({
    url: '/app/imMessage',
    data: {
      id: messageId,
      ...data,
    },
  })
}

/**
 * 新增消息
 */
export const createMessage = (data: {
  conversationId: string
  senderId: string
  receiverId?: string
  receiverType: 'user' | 'group'
  messageType: string
  content: string
  extra?: any
  replyToMessageId?: string
}) => {
  return request.post<{ code: number; message: string; data: { messageId: string; seq: number } }>({
    url: '/app/imMessage',
    data,
  })
}

/**
 * 通过id删除消息
 */
export const deleteMessage = (messageId: string) => {
  return request.delete<{ code: number; message: string }>({
    url: '/app/imMessage',
    data: {
      id: messageId,
    },
  })
}

/**
 * 批量导入消息
 */
export const importMessages = (messages: Array<Partial<Message>>) => {
  return request.post<{ code: number; message: string }>({
    url: '/app/imMessage/import',
    data: {
      messages,
    },
  })
}

/**
 * 分页查询消息
 */
export const getMessagesPage = (params: MessagePageParams = {}) => {
  return request.get<MessageListResponse>({
    url: '/app/imMessage/page',
    data: {
      current: params.current || 1,
      size: params.size || 20,
      conversationId: params.conversationId,
      senderId: params.senderId,
      receiverId: params.receiverId,
      messageType: params.messageType,
      status: params.status,
      startTime: params.startTime,
      endTime: params.endTime,
      keyword: params.keyword,
      beforeSeq: params.beforeSeq,
      afterSeq: params.afterSeq,
    },
  })
}

/**
 * 导出消息列表
 */
export const exportMessages = (params?: MessagePageParams) => {
  return request.get<Blob>({
    url: '/app/imMessage/export',
    data: params,
  })
}

/**
 * 通过条件查询消息详情
 */
export const getMessageDetails = (messageId: string) => {
  return request.get<MessageResponse>({
    url: '/app/imMessage/details',
    data: {
      id: messageId,
    },
  })
}

/**
 * 获取会话消息历史
 */
export const getConversationMessages = (
  conversationId: string,
  params?: {
    size?: number
    beforeSeq?: number
    afterSeq?: number
  }
) => {
  return request.get<MessageListResponse>({
    url: '/app/imMessage/conversation-history',
    data: {
      conversationId,
      size: params?.size || 20,
      beforeSeq: params?.beforeSeq,
      afterSeq: params?.afterSeq,
    },
  })
}

/**
 * 撤回消息
 */
export const recallMessage = (messageId: string, reason?: string) => {
  return request.post<{ code: number; message: string }>({
    url: '/app/imMessage/recall',
    data: {
      messageId,
      reason,
    },
  })
}

/**
 * 标记消息已读
 */
export const markMessageRead = (messageIds: string[]) => {
  return request.post<{ code: number; message: string }>({
    url: '/app/imMessage/mark-read',
    data: {
      messageIds,
    },
  })
}

/**
 * 标记消息已送达
 */
export const markMessageDelivered = (messageIds: string[]) => {
  return request.post<{ code: number; message: string }>({
    url: '/imMessage/mark-delivered',
    data: {
      messageIds,
    },
  })
}

/**
 * 搜索消息
 */
export const searchMessages = (params: {
  keyword: string
  conversationId?: string
  senderId?: string
  messageType?: string
  startTime?: string
  endTime?: string
  limit?: number
}) => {
  return request.get<MessageListResponse>({
    url: '/imMessage/search',
    data: {
      keyword: params.keyword,
      conversationId: params.conversationId,
      senderId: params.senderId,
      messageType: params.messageType,
      startTime: params.startTime,
      endTime: params.endTime,
      limit: params.limit || 50,
    },
  })
}

/**
 * 获取消息统计信息
 */
export const getMessageStatistics = (params?: {
  startTime?: string
  endTime?: string
  conversationId?: string
  messageType?: string
}) => {
  return request.get<{
    code: number
    message: string
    data: {
      totalMessages: number
      textMessages: number
      imageMessages: number
      voiceMessages: number
      videoMessages: number
      fileMessages: number
      todayMessages: number
      weekMessages: number
      monthMessages: number
      typeStats: Array<{ type: string; count: number }>
      dailyStats: Array<{ date: string; count: number }>
    }
  }>({
    url: '/imMessage/statistics',
    data: params,
  })
}

/**
 * 批量删除消息
 */
export const batchDeleteMessages = (messageIds: string[]) => {
  return request.post<{ code: number; message: string }>({
    url: '/imMessage/batch-delete',
    data: {
      messageIds,
    },
  })
}

/**
 * 清空会话消息
 */
export const clearConversationMessages = (conversationId: string) => {
  return request.post<{ code: number; message: string }>({
    url: '/imMessage/clear-conversation',
    data: {
      conversationId,
    },
  })
}

/**
 * 转发消息
 */
export const forwardMessage = (messageId: string, targetConversationIds: string[]) => {
  return request.post<{ code: number; message: string }>({
    url: '/imMessage/forward',
    data: {
      messageId,
      targetConversationIds,
    },
  })
}

/**
 * 获取消息已读回执
 */
export const getMessageReadReceipts = (messageId: string) => {
  return request.get<{
    code: number
    message: string
    data: {
      readCount: number
      unreadCount: number
      readUsers: Array<{
        userId: string
        userName: string
        readTime: string
      }>
      unreadUsers: Array<{
        userId: string
        userName: string
      }>
    }
  }>({
    url: '/imMessage/read-receipts',
    data: {
      messageId,
    },
  })
}

/**
 * 上传文件消息
 */
export const uploadMessageFile = (
  file: File,
  messageType: 'image' | 'voice' | 'video' | 'file'
) => {
  const formData = new FormData()
  formData.append('file', file)
  formData.append('messageType', messageType)

  return request.post<{
    code: number
    message: string
    data: {
      fileUrl: string
      fileName: string
      fileSize: number
      duration?: number // 音视频时长
      width?: number // 图片宽度
      height?: number // 图片高度
    }
  }>({
    url: '/imMessage/upload-file',
    data: formData,
  })
}
