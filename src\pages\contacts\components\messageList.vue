<template>
  <view class="chat-list bg-white relative z-10 rounded-t-[30rpx] overflow-scroll p-[20rpx]">
    <!-- IM登录状态提示 -->
    <!-- <view
      v-if="!imState.isLogin"
      class="im-login-card mb-[30rpx] p-[30rpx] bg-yellow-50 rounded-[20rpx] border border-yellow-200"
    >
      <view class="text-[28rpx] text-yellow-800 mb-[20rpx]"> 💡 需要登录IM才能处理好友申请 </view>
      <c-button size="small" type="primary" @click="quickLoginIM" :loading="imState.isConnecting">
        快速登录IM
      </c-button>
    </view> -->

    <!-- 我的关注标题和筛选 -->
    <view class="flex-row items-center justify-between mb-[24rpx]">
      <!-- 左侧tab切换 -->
      <view class="flex-row items-center p-[6rpx] rounded-[24rpx] bg-[#F4F6FA] mr-[20rpx]">
        <view
          class="justify-center items-center w-[96rpx] h-[54rpx] rounded-[20rpx]"
          :class="[state.queryForm.status === 'all' ? 'bg-white' : '']"
          @click="switchStatus('all')"
        >
          <text
            class="text-[28rpx]"
            :class="[state.queryForm.status === 'all' ? 'bg-white text-title' : 'text-muted']"
            >全部</text
          >
        </view>
        <view
          class="justify-center items-center w-[96rpx] h-[54rpx] rounded-[20rpx]"
          :class="[state.queryForm.status === 'unread' ? 'bg-white' : '']"
          @click="switchStatus('unread')"
        >
          <text
            class="text-[28rpx]"
            :class="[state.queryForm.status === 'unread' ? 'bg-white text-title' : 'text-muted']"
            >未读</text
          >
        </view>
      </view>
      <!-- 右侧一键已读按钮 -->
      <view
        class="flex-row items-center justify-center h-[54rpx] px-[24rpx] rounded-[20rpx] bg-[#F4F6FA]"
        @click="markAllRead"
      >
        <text class="text-[28rpx] text-muted">一键已读</text>
      </view>
    </view>

    <!-- 通知入口区域 -->
    <view class="notification-entries mb-[30rpx]">
      <!-- 系统通知 -->
      <view class="notification-item" @click="navigateToSystemNotifications">
        <view class="notification-avatar">
          <view class="system-notification-icon">
            <text class="notification-icon-text">🔔</text>
          </view>
        </view>
        <view class="notification-content">
          <view class="notification-header">
            <text class="notification-title">系统通知</text>
            <view v-if="systemUnreadCount > 0" class="notification-badge">
              {{ systemUnreadCount }}
            </view>
          </view>
          <text class="notification-message">奶油收藏家赞了你的动态，和她聊聊...</text>
        </view>
      </view>

      <!-- 互动通知 -->
      <view class="notification-item" @click="navigateToInteractionNotifications">
        <view class="notification-avatar">
          <view class="interaction-notification-icon">
            <text class="notification-icon-text">Hi</text>
          </view>
        </view>
        <view class="notification-content">
          <view class="notification-header">
            <text class="notification-title">互动通知</text>
            <view v-if="interactionUnreadCount > 0" class="notification-badge">
              {{ interactionUnreadCount }}
            </view>
          </view>
          <text class="notification-message">又有{{ interactionUnreadCount }}人关注了你</text>
        </view>
      </view>
    </view>

    <messageItem v-for="item in state.dataList" :key="item.id" :data="item"></messageItem>
    <c-loadmore :status="state.loadStatus" @reload="getData()"></c-loadmore>
  </view>
</template>

<script setup lang="ts">
import { reactive, computed, onMounted, onUnmounted } from 'vue'
import messageItem from './messageItem.vue'
import { cloneDeep } from 'lodash-es'
import dayjs from 'dayjs'
// 移除未使用的IM导入
import { message } from '@/utils/util'
// 移除测试用户导入，改为使用真实用户数据
import { notificationManager } from '@/utils/notification'
import { getAllTestUsers } from '@/config/im'

interface IMessage {
  id: string
  avatar: string
  username: string
  lastMessage: string
  time: string
  unreadCount: number
  isGroup?: boolean // 是否为群聊
  groupId?: string // 群聊ID
}

const state = reactive({
  loading: false,
  loadStatus: 'loadend',
  queryForm: {
    current: 1,
    size: 20,
    status: 'all', // 添加状态筛选字段：'all' | 'unread'
  },
  dataList: [] as IMessage[],
  currentFilter: 'all', // 当前筛选状态
})

// 生成模拟消息数据（包含群聊和真实用户）
const generateMockData = (): IMessage[] => {
  const groupData: IMessage[] = [
    {
      id: 'group_1',
      avatar: '/static/images/group/group_avatar.png',
      username: '车友交流群(3)',
      lastMessage: '大家好，欢迎加入车友交流群！',
      time: '09:41',
      unreadCount: 0,
      isGroup: true,
      groupId: 'group_1',
    },
    {
      id: 'group_2',
      avatar: '/static/images/group/group_avatar2.png',
      username: '二手车交易群(3)',
      lastMessage: '有什么好车源推荐一下',
      time: '昨天',
      unreadCount: 2,
      isGroup: true,
      groupId: 'group_2',
    },
  ]

  // 使用真实用户数据生成个人聊天
  const realUsers = getAllTestUsers()
  const userMessages = [
    '你好，请问这辆车还在吗？',
    '价格可以商量吗？',
    '好的，谢谢',
    '我想看看实车',
    '车况怎么样？',
    '可以预约看车吗？',
  ]
  const times = ['09:30', '昨天', '昨天', '星期二', '星期二', '星期一']
  const unreadCounts = [2, 0, 1, 0, 3, 0]

  const userData: IMessage[] = realUsers.map((user, index) => ({
    id: user.userID,
    avatar: user.avatar
      ? `/static/images/user/${user.avatar}`
      : '/static/images/user/default_avatar.png',
    username: user.nickname,
    lastMessage: userMessages[index % userMessages.length],
    time: times[index % times.length],
    unreadCount: unreadCounts[index % unreadCounts.length],
  }))

  return [...groupData, ...userData]
}

const mockData: IMessage[] = generateMockData()

// 计算属性
const systemUnreadCount = computed(() => {
  return notificationManager.getUnreadSystemCount()
})

const interactionUnreadCount = computed(() => {
  return notificationManager.getUnreadInteractionCount()
})

const totalUnreadCount = computed(() => {
  return systemUnreadCount.value + interactionUnreadCount.value
})

const currentFilter = computed(() => {
  return state.currentFilter
})

const getData = function () {
  state.loadStatus = 'loading'
  return new Promise((resolve) => {
    setTimeout(() => {
      const _dataList = cloneDeep(mockData)
      state.dataList.push(
        ..._dataList.map((item) => {
          item.id = item.id + dayjs().valueOf()
          return item
        })
      )
      state.loadStatus = 'loadend'
      resolve(true)
      uni.stopPullDownRefresh()
    }, 200)
  })
}

const initData = function () {
  state.queryForm.current = 1
  state.dataList = []
  getData()
}

const loadMore = function () {
  state.queryForm.current++
  getData()
}

// 检查IM连接状态
const checkIMConnection = () => {
  // 不再自动初始化IM，避免与自动登录冲突
  console.log('消息列表页面已加载，IM状态由自动登录管理')
}

// 快速登录IM（使用自动登录）
const quickLoginIM = async () => {
  try {
    const imManager = await import('@/utils/im/index')
    const success = await imManager.default.autoLogin()

    if (success) {
      message.success('IM登录成功')
      console.log('IM自动登录成功')
    } else {
      message.error('IM登录失败，请检查用户信息')
    }
  } catch (error) {
    console.error('IM登录失败:', error)
    message.error('IM登录失败，请检查配置')
  }
}

// 导航到系统通知
const navigateToSystemNotifications = () => {
  uni.navigateTo({
    url: '/pages/notifications/system',
  })
}

// 导航到互动通知
const navigateToInteractionNotifications = () => {
  uni.navigateTo({
    url: '/pages/notifications/interaction',
  })
}

// 切换筛选
const switchFilter = (filter: string) => {
  state.currentFilter = filter
}

// 切换状态筛选
const switchStatus = (status: string) => {
  state.queryForm.status = status
  // 根据状态筛选数据
  if (status === 'unread') {
    // 筛选出有未读消息的项目
    state.dataList = mockData.filter((item) => item.unreadCount > 0)
  } else {
    // 显示全部数据
    state.dataList = [...mockData]
  }
}

// 标记全部已读
const markAllRead = () => {
  // 标记所有系统通知为已读
  const systemNotifications = notificationManager.getSystemNotifications()
  systemNotifications.forEach((notification) => {
    if (!notification.isRead) {
      notificationManager.markSystemNotificationRead(notification.id)
    }
  })

  // 标记所有互动通知为已读
  const interactionNotifications = notificationManager.getInteractionNotifications()
  interactionNotifications.forEach((notification) => {
    if (!notification.isRead) {
      notificationManager.markInteractionNotificationRead(notification.id)
    }
  })

  uni.showToast({
    title: '已全部标记为已读',
    icon: 'success',
  })
}

// 组件挂载时初始化
onMounted(() => {
  checkIMConnection()
})

// 组件卸载时清理
onUnmounted(() => {
  // 清理监听器等
})

// 初始化数据
initData()

defineExpose({
  loadMore,
  getData,
  initData,
})
</script>

<style scoped lang="scss">
.notification-item {
  display: flex;
  align-items: center;
  padding: 24rpx 20rpx;
  background-color: white;
  border-bottom: 1rpx solid #f0f0f0;
  transition: background-color 0.2s;
  flex-direction: row;
  &:active {
    background-color: #f8f8f8;
  }
}

.notification-avatar {
  margin-right: 24rpx;
}

.system-notification-icon,
.interaction-notification-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.system-notification-icon {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.interaction-notification-icon {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.notification-icon-text {
  font-size: 36rpx;
  color: white;
}

.notification-content {
  flex: 1;
  min-width: 0;
}

.notification-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8rpx;
  flex-direction: row;
}

.notification-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.notification-badge {
  background-color: #ff4757;
  color: white;
  font-size: 20rpx;
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
  min-width: 32rpx;
  text-align: center;
  line-height: 1.2;
}

.notification-message {
  font-size: 28rpx;
  color: #666;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>
