<route lang="json">
{
  "style": {
    "navigationBarTitleText": "系统通知"
  }
}
</route>

<template>
  <view class="system-notifications">
    <!-- 筛选栏 -->
    <view class="filter-bar">
      <view class="filter-tabs">
        <view
          v-for="tab in filterTabs"
          :key="tab.value"
          class="filter-tab"
          :class="{ active: currentFilter === tab.value }"
          @click="switchFilter(tab.value)"
        >
          {{ tab.label }}
        </view>
      </view>
    </view>

    <!-- 通知列表 -->
    <view class="notifications-list">
      <view
        v-for="notification in filteredNotifications"
        :key="notification.id"
        class="notification-item"
        :class="{ unread: !notification.isRead }"
        @click="handleNotificationClick(notification)"
      >
        <view class="notification-icon">
          <text class="icon-text">{{ getNotificationIcon(notification.type) }}</text>
        </view>
        <view class="notification-content">
          <view class="notification-header">
            <text class="notification-title">{{ notification.title }}</text>
            <text class="notification-time">{{ notification.time }}</text>
          </view>
          <text class="notification-description">{{ notification.description }}</text>
        </view>
        <view v-if="!notification.isRead" class="unread-dot"></view>
      </view>
    </view>

    <!-- 空状态 -->
    <view v-if="filteredNotifications.length === 0" class="empty-state">
      <text class="empty-text">暂无通知</text>
    </view>

    <!-- 底部操作 -->
    <view class="bottom-actions">
      <view class="action-btn" @click="markAllRead">
        <text>全部已读</text>
      </view>
      <view class="action-btn" @click="clearAll">
        <text>清空通知</text>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { notificationManager, type SystemNotification } from '@/utils/notification'

// 筛选标签
const filterTabs = [
  { label: '全部', value: 'all' },
  { label: '系统通知', value: '系统通知' },
  { label: '订单通知', value: '订单通知' },
  { label: '消息通知', value: '消息通知' },
  { label: '退款通知', value: '退款通知' },
]

const currentFilter = ref('all')
const notifications = ref<SystemNotification[]>([])

// 过滤后的通知列表
const filteredNotifications = computed(() => {
  if (currentFilter.value === 'all') {
    return notifications.value
  }
  return notifications.value.filter(n => n.type === currentFilter.value)
})

// 切换筛选
const switchFilter = (filter: string) => {
  currentFilter.value = filter
}

// 获取通知图标
const getNotificationIcon = (type: string) => {
  const iconMap: Record<string, string> = {
    '系统通知': '🔔',
    '订单通知': '📦',
    '消息通知': '💬',
    '退款通知': '💰',
  }
  return iconMap[type] || '📢'
}

// 处理通知点击
const handleNotificationClick = (notification: SystemNotification) => {
  // 标记为已读
  if (!notification.isRead) {
    notificationManager.markSystemNotificationRead(notification.id)
    notification.isRead = true
  }

  // 跳转到详情页面
  if (notification.detailUrl) {
    uni.navigateTo({
      url: notification.detailUrl,
    })
  } else {
    uni.showToast({
      title: '查看详情',
      icon: 'none',
    })
  }
}

// 标记全部已读
const markAllRead = () => {
  notifications.value.forEach(notification => {
    if (!notification.isRead) {
      notificationManager.markSystemNotificationRead(notification.id)
      notification.isRead = true
    }
  })
  
  uni.showToast({
    title: '已全部标记为已读',
    icon: 'success',
  })
}

// 清空所有通知
const clearAll = () => {
  uni.showModal({
    title: '确认清空',
    content: '确定要清空所有系统通知吗？',
    success: (res) => {
      if (res.confirm) {
        notifications.value = []
        uni.showToast({
          title: '已清空所有通知',
          icon: 'success',
        })
      }
    },
  })
}

// 加载通知数据
const loadNotifications = () => {
  notifications.value = notificationManager.getSystemNotifications()
}

onMounted(() => {
  loadNotifications()
  
  // 监听新通知
  notificationManager.addListener((type, data) => {
    if (type === 'system') {
      loadNotifications()
    }
  })
})
</script>

<style scoped>
.system-notifications {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.filter-bar {
  background: white;
  padding: 20rpx;
  border-bottom: 1rpx solid #eee;
}

.filter-tabs {
  display: flex;
  gap: 20rpx;
}

.filter-tab {
  padding: 12rpx 24rpx;
  border-radius: 20rpx;
  background: #f0f0f0;
  color: #666;
  font-size: 28rpx;
  transition: all 0.3s;
}

.filter-tab.active {
  background: #007aff;
  color: white;
}

.notifications-list {
  padding: 20rpx;
}

.notification-item {
  display: flex;
  align-items: flex-start;
  padding: 30rpx;
  margin-bottom: 20rpx;
  background: white;
  border-radius: 16rpx;
  position: relative;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.notification-item.unread {
  border-left: 6rpx solid #007aff;
}

.notification-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 40rpx;
  background: #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
}

.icon-text {
  font-size: 36rpx;
}

.notification-content {
  flex: 1;
}

.notification-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx;
}

.notification-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.notification-time {
  font-size: 24rpx;
  color: #999;
}

.notification-description {
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
}

.unread-dot {
  width: 16rpx;
  height: 16rpx;
  border-radius: 8rpx;
  background: #ff3b30;
  position: absolute;
  top: 30rpx;
  right: 30rpx;
}

.empty-state {
  text-align: center;
  padding: 120rpx 0;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}

.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  background: white;
  border-top: 1rpx solid #eee;
  padding: 20rpx;
  gap: 20rpx;
}

.action-btn {
  flex: 1;
  padding: 24rpx;
  text-align: center;
  background: #f0f0f0;
  border-radius: 12rpx;
  color: #333;
  font-size: 28rpx;
}

.action-btn:active {
  background: #e0e0e0;
}
</style>
