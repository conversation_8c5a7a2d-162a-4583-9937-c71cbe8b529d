<route lang="json">
{
  "style": {
    "navigationBarTitleText": "互动通知"
  }
}
</route>

<template>
  <view class="interaction-notifications">
    <!-- 筛选栏 -->
    <view class="filter-bar">
      <view class="filter-tabs">
        <view
          v-for="tab in filterTabs"
          :key="tab.value"
          class="filter-tab"
          :class="{ active: currentFilter === tab.value }"
          @click="switchFilter(tab.value)"
        >
          {{ tab.label }}
        </view>
      </view>
    </view>

    <!-- 通知列表 -->
    <view class="notifications-list">
      <view
        v-for="notification in filteredNotifications"
        :key="notification.id"
        class="notification-item"
        :class="{ unread: !notification.isRead }"
        @click="handleNotificationClick(notification)"
      >
        <view class="user-avatar">
          <image :src="notification.userAvatar" mode="aspectFill" />
        </view>
        <view class="notification-content">
          <view class="notification-header">
            <text class="user-name">{{ notification.userName }}</text>
            <text class="notification-time">{{ notification.time }}</text>
          </view>
          <text class="notification-content-text">{{ notification.content }}</text>
          <text class="interaction-type">{{ getInteractionTypeText(notification.type) }}</text>
        </view>
        <view class="action-area">
          <view
            v-if="notification.type === 'follow'"
            class="follow-btn"
            :class="{ followed: isFollowed(notification.userId) }"
            @click.stop="handleFollow(notification)"
          >
            <text>{{ isFollowed(notification.userId) ? '已关注' : '关注' }}</text>
          </view>
          <view v-else class="interaction-icon">
            <text>{{ getInteractionIcon(notification.type) }}</text>
          </view>
        </view>
        <view v-if="!notification.isRead" class="unread-dot"></view>
      </view>
    </view>

    <!-- 空状态 -->
    <view v-if="filteredNotifications.length === 0" class="empty-state">
      <text class="empty-text">暂无互动通知</text>
    </view>

    <!-- 底部操作 -->
    <view class="bottom-actions">
      <view class="action-btn" @click="markAllRead">
        <text>全部已读</text>
      </view>
      <view class="action-btn" @click="clearAll">
        <text>清空通知</text>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { notificationManager, type InteractionNotification } from '@/utils/notification'

// 筛选标签
const filterTabs = [
  { label: '全部', value: 'all' },
  { label: '关注', value: 'follow' },
  { label: '点赞', value: 'like' },
  { label: '评论', value: 'comment' },
  { label: '分享', value: 'share' },
]

const currentFilter = ref('all')
const notifications = ref<InteractionNotification[]>([])
const followedUsers = ref<Set<string>>(new Set())

// 过滤后的通知列表
const filteredNotifications = computed(() => {
  if (currentFilter.value === 'all') {
    return notifications.value
  }
  return notifications.value.filter(n => n.type === currentFilter.value)
})

// 切换筛选
const switchFilter = (filter: string) => {
  currentFilter.value = filter
}

// 获取互动类型文本
const getInteractionTypeText = (type: string) => {
  const typeMap: Record<string, string> = {
    follow: '关注了你',
    like: '赞了你的动态',
    comment: '评论了你的动态',
    share: '分享了你的动态',
  }
  return typeMap[type] || '与你互动'
}

// 获取互动图标
const getInteractionIcon = (type: string) => {
  const iconMap: Record<string, string> = {
    like: '❤️',
    comment: '💬',
    share: '🔗',
  }
  return iconMap[type] || '👍'
}

// 检查是否已关注
const isFollowed = (userId: string) => {
  return followedUsers.value.has(userId)
}

// 处理关注操作
const handleFollow = (notification: InteractionNotification) => {
  const userId = notification.userId
  if (followedUsers.value.has(userId)) {
    followedUsers.value.delete(userId)
    uni.showToast({
      title: '已取消关注',
      icon: 'none',
    })
  } else {
    followedUsers.value.add(userId)
    uni.showToast({
      title: '关注成功',
      icon: 'success',
    })
  }
}

// 处理通知点击
const handleNotificationClick = (notification: InteractionNotification) => {
  // 标记为已读
  if (!notification.isRead) {
    notificationManager.markInteractionNotificationRead(notification.id)
    notification.isRead = true
  }

  // 根据类型跳转到相应页面
  switch (notification.type) {
    case 'follow':
      // 跳转到用户主页
      uni.navigateTo({
        url: `/pages/user-profile/user-profile?userId=${notification.userId}`,
      })
      break
    case 'like':
    case 'comment':
    case 'share':
      // 跳转到动态详情
      uni.showToast({
        title: '查看动态详情',
        icon: 'none',
      })
      break
    default:
      break
  }
}

// 标记全部已读
const markAllRead = () => {
  notifications.value.forEach(notification => {
    if (!notification.isRead) {
      notificationManager.markInteractionNotificationRead(notification.id)
      notification.isRead = true
    }
  })
  
  uni.showToast({
    title: '已全部标记为已读',
    icon: 'success',
  })
}

// 清空所有通知
const clearAll = () => {
  uni.showModal({
    title: '确认清空',
    content: '确定要清空所有互动通知吗？',
    success: (res) => {
      if (res.confirm) {
        notifications.value = []
        uni.showToast({
          title: '已清空所有通知',
          icon: 'success',
        })
      }
    },
  })
}

// 加载通知数据
const loadNotifications = () => {
  notifications.value = notificationManager.getInteractionNotifications()
}

onMounted(() => {
  loadNotifications()
  
  // 监听新通知
  notificationManager.addListener((type, data) => {
    if (type === 'interaction') {
      loadNotifications()
    }
  })
})
</script>

<style scoped>
.interaction-notifications {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.filter-bar {
  background: white;
  padding: 20rpx;
  border-bottom: 1rpx solid #eee;
}

.filter-tabs {
  display: flex;
  gap: 20rpx;
}

.filter-tab {
  padding: 12rpx 24rpx;
  border-radius: 20rpx;
  background: #f0f0f0;
  color: #666;
  font-size: 28rpx;
  transition: all 0.3s;
}

.filter-tab.active {
  background: #007aff;
  color: white;
}

.notifications-list {
  padding: 20rpx;
}

.notification-item {
  display: flex;
  align-items: center;
  padding: 30rpx;
  margin-bottom: 20rpx;
  background: white;
  border-radius: 16rpx;
  position: relative;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.notification-item.unread {
  border-left: 6rpx solid #007aff;
}

.user-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 40rpx;
  overflow: hidden;
  margin-right: 24rpx;
}

.user-avatar image {
  width: 100%;
  height: 100%;
}

.notification-content {
  flex: 1;
}

.notification-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8rpx;
}

.user-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.notification-time {
  font-size: 24rpx;
  color: #999;
}

.notification-content-text {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 8rpx;
}

.interaction-type {
  font-size: 24rpx;
  color: #999;
}

.action-area {
  margin-left: 20rpx;
}

.follow-btn {
  padding: 12rpx 24rpx;
  border-radius: 20rpx;
  background: #007aff;
  color: white;
  font-size: 24rpx;
  text-align: center;
  min-width: 80rpx;
}

.follow-btn.followed {
  background: #f0f0f0;
  color: #666;
}

.interaction-icon {
  font-size: 32rpx;
  text-align: center;
}

.unread-dot {
  width: 16rpx;
  height: 16rpx;
  border-radius: 8rpx;
  background: #ff3b30;
  position: absolute;
  top: 30rpx;
  right: 30rpx;
}

.empty-state {
  text-align: center;
  padding: 120rpx 0;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}

.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  background: white;
  border-top: 1rpx solid #eee;
  padding: 20rpx;
  gap: 20rpx;
}

.action-btn {
  flex: 1;
  padding: 24rpx;
  text-align: center;
  background: #f0f0f0;
  border-radius: 12rpx;
  color: #333;
  font-size: 28rpx;
}

.action-btn:active {
  background: #e0e0e0;
}
</style>
