# 📂 车城 uni-app 功能-代码映射报告

## 🏗️ 项目概览

- **技术栈**: uni-app + Vue 3 + TypeScript + Vite
- **架构模式**: 页面组件化架构，支持多端发布（H5、小程序、App）
- **状态管理**: Pinia + 组合式 API
- **样式方案**: Tailwind CSS + SCSS + uni-app 内置样式
- **构建工具**: Vite + uni-app CLI
- **包管理**: yarn

## 📊 功能模块统计

- **页面级组件**: 15+ 个主要页面（首页、圈子、消息、个人中心等）
- **可复用组件**: 30+ 个通用 UI 组件（按钮、输入框、图片、列表等）
- **业务逻辑模块**: 6 个 API 模块（用户、新闻、应用、商城等）
- **样式文件**: Tailwind + SCSS 混合方案
- **配置文件**: 路由配置、环境配置、构建配置

## 🗂️ 目录结构概览

```
carcity-app/
├── src/
│   ├── pages/          # 主包页面
│   ├── clique/         # 圈子分包
│   ├── user/           # 用户分包
│   ├── components/     # 通用组件
│   ├── api/           # 接口定义
│   ├── stores/        # 状态管理
│   └── utils/         # 工具函数
├── static/            # 静态资源
└── 配置文件
```

---

## 🎯 功能映射表

### 导航栏 - 底部标签栏

**🔤 用户描述方式**:

- 主要: "底部导航", "标签栏", "底部菜单", "主导航"
- 别名: "tabbar", "底部按钮", "主菜单", "导航条"

**📍 代码位置**:

- 配置: `pages.config.ts` 第 48-74 行 - tabBar 配置
- 组件: `src/components/c-tabbar/c-tabbar.vue` - 自定义标签栏组件
- 图标: `static/icons/` - 导航图标资源

**🎨 视觉标识**:

- 外观: 底部固定，4 个标签（车源、圈子、消息、我的）
- 文本: "车源"、"圈子"、"消息"、"我的"

**⚡ 修改指引**:

- 修改标签文字: 编辑 `pages.config.ts` 中的 `text` 字段
- 修改图标: 替换 `static/icons/` 中对应图标文件
- 修改页面跳转: 编辑 `pagePath` 字段

---

### 用户功能 - 登录页面

**🔤 用户描述方式**:

- 主要: "登录", "登录页", "用户登录", "账号登录"
- 别名: "sign in", "验证码登录", "密码登录", "微信登录"

**📍 代码位置**:

- 主文件: `src/user/accountLogin.vue` - 账号密码登录页面
- 微信登录: `src/user/authLogin.vue` - 微信授权登录
- 业务逻辑: `src/api/account.ts` - 登录相关 API
- 状态管理: `src/stores/user.ts` - 用户状态管理

**🎨 视觉标识**:

- 外观: 蓝色渐变背景，白色输入框，蓝色登录按钮
- 文本: "您好！欢迎登录车富强"、"立即登录"、"微信登录"

**⚡ 修改指引**:

- 修改登录方式: 编辑 `accountLogin.vue` 中的登录表单
- 修改样式: 编辑对应 vue 文件的 style 部分
- 修改登录逻辑: 编辑 `api/account.ts` 中的登录方法

---

### 用户功能 - 个人中心

**🔤 用户描述方式**:

- 主要: "个人中心", "我的", "用户中心", "个人页面"
- 别名: "profile", "用户信息", "个人资料", "我的页面"

**📍 代码位置**:

- 主文件: `src/pages/user/user.vue` - 个人中心主页
- 用户详情: `src/pages/user/profile.vue` - 用户详情页面（支持动态标题栏显示用户名，发消息和关注功能）
- 资料编辑: `src/pages/user_data/user_data.vue` - 个人资料页
- 设置页面: `src/user/user_set.vue` - 用户设置页（已添加黑名单管理入口）
- 头像组件: `src/components/avatar-upload/avatar-upload.vue`

**🎨 视觉标识**:

- 外观: 蓝色渐变头部，圆形头像，白色卡片布局
- 文本: "个人中心"、"编辑资料"、"关注"、"粉丝"、"收藏"

**⚡ 修改指引**:

- 修改头像: 编辑 `user.vue` 中的头像显示逻辑
- 修改统计数据: 编辑用户信息展示区域
- 修改菜单项: 编辑 `c-list-item` 组件配置

---

### 搜索功能 - 搜索页面

**🔤 用户描述方式**:

- 主要: "搜索", "搜索框", "搜索页面", "查找"
- 别名: "search", "搜索功能", "关键词搜索", "内容搜索"

**📍 代码位置**:

- 主文件: `src/pages/search/search.vue` - 搜索主页面
- 搜索组件: `src/components/c-search/c-search.vue` - 搜索框组件
- 搜索建议: `src/pages/search/component/suggest.vue` - 搜索建议组件
- 搜索 API: `src/api/news.ts` 中的 `getArticleList` 方法

**🎨 视觉标识**:

- 外观: 白色搜索框，灰色占位符，搜索结果列表
- 文本: "请输入关键词搜索"、搜索历史、热门搜索

**⚡ 修改指引**:

- 修改搜索框样式: 编辑 `c-search.vue` 组件
- 修改搜索逻辑: 编辑 `search.vue` 中的 `handleSearch` 方法
- 修改搜索结果: 编辑 `queryList` 方法和结果展示

---

### 内容展示 - 车源列表

**🔤 用户描述方式**:

- 主要: "车源", "车源列表", "首页", "车辆信息"
- 别名: "车辆列表", "汽车信息", "车源圈", "主页"

**📍 代码位置**:

- 主文件: `src/pages/index/index.vue` - 车源首页
- 列表项: `src/components/infoItem/infoItem.vue` - 车源信息卡片
- 数据获取: `src/api/home.ts` - 首页数据 API
- 搜索组件: `src/components/c-search/c-search.vue`

**🎨 视觉标识**:

- 外观: 蓝色渐变头部，白色卡片列表，圆角设计
- 文本: "车源圈"、车辆品牌型号、价格、位置信息

**⚡ 修改指引**:

- 修改头部样式: 编辑 `index.vue` 中的 header 部分
- 修改车源卡片: 编辑 `infoItem.vue` 组件
- 修改数据加载: 编辑 `api/home.ts` 中的接口

---

### 内容展示 - 圈子功能

**🔤 用户描述方式**:

- 主要: "圈子", "圈子列表", "社区", "动态"
- 别名: "社交圈", "朋友圈", "动态列表", "圈子页面"

**📍 代码位置**:

- 主文件: `src/pages/clique/list.vue` - 圈子列表页
- 分包页面: `clique/` 目录下的相关页面
- 信息列表: `src/clique/components/infoList.vue`
- 需求列表: `src/clique/components/demandList.vue`

**🎨 视觉标识**:

- 外观: 标签切换，卡片式布局，用户头像和内容
- 文本: "圈子"、用户昵称、发布内容、时间

**⚡ 修改指引**:

- 修改圈子布局: 编辑 `clique/list.vue` 页面
- 修改内容卡片: 编辑对应的组件文件
- 修改数据获取: 编辑相关 API 接口

---

### 内容展示 - 消息列表

**🔤 用户描述方式**:

- 主要: "消息", "消息列表", "聊天", "通知"
- 别名: "message", "聊天记录", "消息中心", "通知列表"

**📍 代码位置**:

- 主文件: `src/pages/chat/list.vue` - 消息列表页
- 聊天组件: `src/pages/chat/components/` - 聊天相关组件
- 消息 API: 相关的消息接口（需要进一步查看）

**🎨 视觉标识**:

- 外观: 列表式布局，头像、用户名、最后消息
- 文本: "消息"、联系人姓名、最新消息内容

**⚡ 修改指引**:

- 修改消息列表: 编辑 `chat/list.vue` 页面
- 修改消息样式: 编辑聊天组件样式
- 修改消息逻辑: 编辑消息相关 API

---

### 表单组件 - 通用按钮

**🔤 用户描述方式**:

- 主要: "按钮", "提交按钮", "确认按钮", "登录按钮"
- 别名: "button", "点击按钮", "操作按钮", "功能按钮"

**📍 代码位置**:

- 主文件: `src/components/c-button/c-button.vue` - 通用按钮组件
- 样式定义: 组件内的 style 部分
- 使用示例: 各页面中的按钮使用

**🎨 视觉标识**:

- 外观: 圆角矩形，多种颜色主题（primary、success 等）
- 文本: 根据使用场景显示不同文字

**⚡ 修改指引**:

- 修改按钮样式: 编辑 `c-button.vue` 中的 CSS 类
- 修改按钮行为: 编辑组件的点击事件处理
- 添加新样式: 在组件中添加新的 type 类型

---

### 表单组件 - 输入框

**🔤 用户描述方式**:

- 主要: "输入框", "文本框", "表单输入", "输入字段"
- 别名: "input", "文本输入", "表单控件", "输入组件"

**📍 代码位置**:

- 主文件: `src/components/c-input/c-input.vue` - 通用输入框组件
- 表单组件: `src/components/c-form/c-form.vue` - 表单容器
- 表单项: `src/components/c-form-item/c-form-item.vue` - 表单项组件

**🎨 视觉标识**:

- 外观: 白色背景，灰色边框，圆角设计
- 文本: 占位符文字，输入内容

**⚡ 修改指引**:

- 修改输入框样式: 编辑 `c-input.vue` 组件样式
- 修改表单布局: 编辑 `c-form.vue` 和 `c-form-item.vue`
- 修改验证逻辑: 在使用页面中添加验证规则

---

### 媒体组件 - 图片显示

**🔤 用户描述方式**:

- 主要: "图片", "头像", "图片显示", "照片"
- 别名: "image", "图像", "头像图片", "用户照片"

**📍 代码位置**:

- 主文件: `src/components/c-img/c-img.vue` - 通用图片组件
- 头像上传: `src/components/avatar-upload/avatar-upload.vue` - 头像上传组件
- 图片上传: `src/components/c-upload/c-upload.vue` - 文件上传组件

**🎨 视觉标识**:

- 外观: 根据使用场景显示不同尺寸和形状
- 默认图片: 用户默认头像等占位图

**⚡ 修改指引**:

- 修改图片样式: 编辑 `c-img.vue` 组件
- 修改默认图片: 替换 `static/` 目录中的默认图片
- 修改上传逻辑: 编辑上传组件的处理方法

---

### 列表组件 - 通用列表

**🔤 用户描述方式**:

- 主要: "列表", "列表项", "菜单列表", "选项列表"
- 别名: "list", "菜单项", "列表条目", "选择项"

**📍 代码位置**:

- 列表项: `src/components/c-list-item/c-list-item.vue` - 通用列表项
- 索引列表: `src/components/c-list-index/c-list-index.vue` - 索引列表
- 新闻卡片: `src/components/news-card/news-card.vue` - 新闻列表项

**🎨 视觉标识**:

- 外观: 白色背景，分割线，右箭头图标
- 文本: 标题、副标题、图标

**⚡ 修改指引**:

- 修改列表样式: 编辑对应的列表组件
- 修改分割线: 编辑组件中的边框样式
- 修改图标: 编辑列表项中的图标显示

---

### 导航组件 - 标题栏

**🔤 用户描述方式**:

- 主要: "标题栏", "导航栏", "页面标题", "顶部导航"
- 别名: "title bar", "header", "页面头部", "导航头"

**📍 代码位置**:

- 主文件: `src/components/c-titlebar/c-titlebar.vue` - 自定义标题栏
- 状态栏: `src/components/c-statusbar/c-statusbar.vue` - 状态栏组件
- 页面配置: `src/pages.json` - 原生导航栏配置

**🎨 视觉标识**:

- 外观: 白色背景，居中标题，返回按钮
- 文本: 页面标题文字

**⚡ 修改指引**:

- 修改标题栏样式: 编辑 `c-titlebar.vue` 组件
- 修改返回按钮: 编辑组件中的返回逻辑
- 修改标题文字: 在页面中传入 title 属性

---

### 聊天功能 - 即时通信系统

**🔤 用户描述方式**:

- 主要: "聊天", "即时通信", "IM", "消息", "私聊", "群聊"
- 别名: "聊天室", "对话", "会话", "消息发送", "实时聊天"

**📍 代码位置**:

- 聊天列表: `src/pages/chat/list.vue` - 聊天会话列表页面
- 消息页面: `src/pages/contacts/contacts.vue` - 消息主页面（包含消息、通讯录两个 tab，带快速登录 IM 功能）
- 消息列表: `src/pages/contacts/components/messageList.vue` - 消息列表组件（已修复快速登录 IM 功能，使用配置文件中的固定 userSig，已添加所有聊天功能页面入口，包含系统通知和互动通知入口，已更新为使用真实用户数据）
- 通讯录列表: `src/pages/contacts/components/contactsList.vue` - 通讯录列表组件（包含好友列表，已修复快速登录 IM 功能，使用配置文件中的固定 userSig，已添加通讯录功能入口，已更新为使用真实用户数据）
- 系统通知: 已集成到消息列表中（系统通知、订单通知、消息通知、退款通知，支持筛选和标记已读）
- 互动通知: 已集成到消息列表中（关注、点赞、评论、分享通知，支持筛选和跳转用户详情）
- 聊天页面: `src/pages/chat/chat.vue` - 单聊/群聊界面（已完善输入功能：语音、表情、常用语、更多功能，已添加设置按钮路由，更多功能面板包含开发测试入口，**已升级为腾讯云 IM SDK**）
- 聊天设置: `src/pages/chat/settings.vue` - 用户聊天设置页面（已更新为使用真实用户数据）
- 群聊信息: `src/pages/chat/group-info.vue` - 群聊信息管理页面（群成员展示、群设置、危险操作，已更新为 3 人真实群聊）

- 群聊创建: `src/pages/chat/create-group.vue` - 创建新群聊页面（选择成员、设置群信息）
- 群成员管理: `src/pages/chat/group-members.vue` - 群成员管理页面（添加/删除成员、设置管理员）
- 好友申请详情: `src/pages/chat/friend-request.vue` - 处理好友申请页面（同意/拒绝、设置备注）
- 消息转发: `src/pages/chat/forward-message.vue` - 消息转发页面（选择转发目标）
- 聊天文件管理: `src/pages/chat/chat-files.vue` - 查看聊天中的文件（图片、文档、语音等）
- 搜索聊天记录: `src/pages/chat/search-messages.vue` - 搜索历史聊天记录
- 黑名单管理: `src/pages/chat/blacklist.vue` - 黑名单用户管理页面
- 投诉举报: `src/pages/report/report.vue` - 投诉举报功能

- 群组管理 API 服务: `src/api/group.ts` - 群组管理相关 API 接口
- IM 用户管理 API 服务: `src/api/imUser.ts` - IM 用户管理相关 API 接口
- 用户通知 API 服务: `src/api/userNotification.ts` - 用户通知管理相关 API 接口
- 会话管理 API 服务: `src/api/conversation.ts` - 会话管理相关 API 接口
- 消息管理 API 服务: `src/api/message.ts` - 消息管理相关 API 接口
- 用户关注管理 API 服务: `src/api/userFollow.ts` - 用户关注管理相关 API 接口
- IM SDK: `src/utils/im/index.ts` - 腾讯云 IM SDK 集成（**已升级为腾讯云 IM SDK，整合了所有 IM 功能**）
- IM 配置: `src/config/im.ts` - IM 统一配置文件（包含 3 个真实用户数据：user1(测试账号一)/user2(测试账号二)/user3(测试 333)，包含真实的 userSig 和用户信息，包含模拟好友申请数据，包含日志级别配置、消息类型、会话类型等）
- IM 初始化: `src/utils/im/init.ts` - IM 初始化工具（应用启动时自动初始化 IM）
- 推送通知: `src/utils/push/` - 消息推送管理工具
- 聊天 API: `src/api/chat.ts` - 聊天相关接口

## 📋 API 接口映射详情

### 🔐 用户管理模块

#### IM 用户管理 (`src/api/imUser.ts`)

- **接口前缀**: `/imUser/*`
- **核心功能**:
  - `PUT /imUser` - 修改用户信息
  - `POST /imUser` - 新增用户
  - `DELETE /imUser` - 删除用户
  - `GET /imUser/page` - 分页查询用户
  - `POST /imUser/forceOffline` - 强制用户下线
  - `POST /imUser/sendSystemMessage` - 发送系统消息
  - `GET /imUser/statistics` - 获取用户统计信息
- **使用场景**: 用户资料管理、状态监控、系统管理

#### 用户关注管理 (`src/api/userFollow.ts`)

- **接口前缀**: `/imUserFollow/*`
- **核心功能**:
  - `POST /imUserFollow/imUserFollowAdd` - 新增用户关注
  - `POST /imUserFollow/imUserFollowCancel` - 取消用户关注
  - `POST /imUserFollow/updateRemarkName` - 修改好友备注
  - `POST /imUserFollow/noDisturbingConfig` - 设置免打扰
  - `GET /imUserFollow/page` - 分页查询关注列表
  - `GET /imUserFollow/check-relation` - 检查关注关系
  - `POST /imUserFollow/batch-follow` - 批量关注用户
- **使用场景**: 社交关系、好友管理、关注列表

### 🏢 群组管理模块

#### 群组管理 (`src/api/group.ts`)

- **接口前缀**: `/imGroup/*`
- **核心功能**:
  - `PUT /imGroup` - 修改群组信息
  - `POST /imGroup` - 新增群组
  - `DELETE /imGroup` - 删除群组
  - `GET /imGroup/page` - 分页查询群组
  - `POST /imGroup/dissolve` - 解散群组
  - `POST /imGroup/transfer-owner` - 转让群主
  - `GET /imGroup/statistics` - 获取群组统计
- **使用场景**: 群组创建、群设置、群管理

### 💬 消息通信模块

#### 会话管理 (`src/api/conversation.ts`)

- **接口前缀**: `/imConversation/*`
- **核心功能**:
  - `PUT /imConversation` - 修改会话
  - `POST /imConversation` - 新增会话
  - `DELETE /imConversation` - 删除会话
  - `POST /imConversation/topUserConfig` - 设置置顶用户(单聊)
  - `POST /imConversation/topGroupConfig` - 设置置顶群聊
  - `POST /imConversation/initialize` - 初始化聊天
  - `GET /imConversation/page` - 分页查询会话
- **使用场景**: 聊天会话、会话列表、会话设置

#### 消息管理 (`src/api/message.ts`)

- **接口前缀**: `/imMessage/*`
- **核心功能**:
  - `PUT /imMessage` - 修改消息
  - `POST /imMessage` - 新增消息
  - `DELETE /imMessage` - 删除消息
  - `POST /imMessage/recall` - 撤回消息
  - `POST /imMessage/mark-read` - 标记消息已读
  - `POST /imMessage/upload-file` - 上传文件消息
  - `GET /imMessage/search` - 搜索消息
  - `GET /imMessage/statistics` - 获取消息统计
- **使用场景**: 消息发送、消息历史、文件传输

### 🔔 通知系统模块

#### 用户通知管理 (`src/api/userNotification.ts`)

- **接口前缀**: `/userNotification/*`
- **核心功能**:
  - `PUT /userNotification` - 修改通知
  - `POST /userNotification` - 新增通知
  - `DELETE /userNotification` - 删除通知
  - `POST /userNotification/markRead` - 标记已读
  - `POST /userNotification/markAllRead` - 全部标记已读
  - `GET /userNotification/page` - 分页查询通知
  - `GET /userNotification/unreadCount` - 获取未读数量
  - `GET /userNotification/statistics` - 获取通知统计
- **使用场景**: 系统通知、互动通知、消息推送

**🎨 视觉标识**:

- 外观: 微信风格聊天界面，消息气泡，用户头像，时间戳
- 文本: 聊天内容、用户昵称、消息状态（已读/未读）

**⚡ 修改指引**:

- 修改聊天界面: 编辑 `chat.vue` 中的消息展示和输入组件
- 修改聊天设置: 编辑 `settings.vue` 中的设置选项
- 修改群聊管理: 编辑 `group-info.vue` 中的群组功能
- 修改黑名单管理: 编辑 `blacklist.vue` 中的黑名单列表和操作
- 修改 IM 配置: 编辑 `src/utils/im/` 中的 SDK 配置
- 修改日志级别: 编辑 `src/utils/im/config.ts` 中的 LOG_LEVEL 配置

**🚀 功能特性**:

- ✅ 用户单聊 - 一对一即时通信
- ✅ 群聊功能 - 多人群组聊天
- ✅ 消息已读状态 - 显示消息读取状态
- ✅ 聊天设置 - 免打扰、黑名单、备注名
- ✅ 黑名单管理 - 查看和管理所有被拉黑的用户
- ✅ 群聊管理 - 成员管理、群设置、退出群聊
- ✅ 投诉举报 - 用户/群组举报功能
- ✅ 腾讯云 IM 集成 - 企业级 IM 解决方案

**📱 页面路由**:

- 聊天列表: `/pages/chat/list`
- 单聊页面: `/pages/chat/chat?userId=xxx&username=xxx`
- 群聊页面: `/pages/chat/chat?groupId=xxx&groupName=xxx`
- 聊天设置: `/pages/chat/settings?userId=xxx&username=xxx`
- 群聊信息: `/pages/chat/group-info?groupId=xxx&groupName=xxx`

- 群聊创建: `/pages/chat/create-group`
- 群成员管理: `/pages/chat/group-members?groupId=xxx&groupName=xxx`
- 好友申请详情: `/pages/chat/friend-request?requestData=xxx`
- 消息转发: `/pages/chat/forward-message?content=xxx&messageId=xxx`
- 聊天文件管理: `/pages/chat/chat-files?conversationId=xxx&conversationName=xxx`
- 搜索聊天记录: `/pages/chat/search-messages?conversationId=xxx&conversationName=xxx`
- 黑名单管理: `/pages/chat/blacklist`
- 投诉举报: `/pages/report/report?userId=xxx` 或 `?groupId=xxx`

**🚀 功能入口导航**:

在消息页面的两个 tab 中，已添加完整的功能入口导航：

1. **消息 tab** (`messageList.vue`):

   - 🚀 聊天功能入口区域
   - 主要功能: 聊天页面、聊天列表、聊天设置、群聊信息

2. **通讯录 tab** (`contactsList.vue`):

   - 👥 通讯录功能区域
   - 功能按钮: 开始聊天、创建群聊、举报用户

**🔧 日志级别配置优化**:

已修复腾讯云 IM SDK 的日志级别警告问题：

- 统一使用 `IM_CONFIG.LOG_LEVEL` 配置常量
- 开发环境自动使用 VERBOSE 级别（详细日志）
- 生产环境自动使用 RELEASE 级别（关键信息）
- 支持 5 个日志级别：VERBOSE(0)、RELEASE(1)、WARNING(2)、ERROR(3)、SILENT(4)

**🚫 黑名单管理功能**:

新增黑名单管理页面 (`src/pages/chat/blacklist.vue`)：

- ✅ 显示所有被拉黑的用户列表
- ✅ 显示用户头像、昵称、认证标识
- ✅ 显示拉黑时间（今天、昨天、X 天前、具体日期）
- ✅ 支持解除拉黑操作（带确认弹窗）
- ✅ 空状态提示（暂无黑名单用户）
- ✅ 用户数量统计显示

**📱 页面入口**:

- 聊天设置页面: `聊天设置` → `黑名单管理`
- 用户设置页面: `个人中心` → `设置` → `黑名单管理`

**🎨 设计特点**:

- 微信风格的列表设计
- 清晰的用户信息展示
- 友好的时间格式化显示
- 简洁的操作按钮设计

---

## 🚀 使用说明

### 对于用户

当你想修改某个功能时，只需告诉 AI：

- "我想修改登录按钮的颜色" → 定位到 `c-button.vue` 组件
- "搜索框的占位符文字需要改" → 定位到 `c-search.vue` 组件
- "个人中心的头像太小了" → 定位到 `pages/user/user.vue` 页面
- "底部导航的图标要换" → 定位到 `static/icons/` 目录

### 对于 AI

收到用户需求后：

1. 在映射表中搜索相关的"用户描述方式"
2. 定位到对应的"代码位置"
3. 根据"修改指引"提供具体的修改方案
4. 使用相应工具进行精确修改

---

## 📝 补充说明

这份映射表覆盖了车城 app 的所有主要用户可感知功能，包括：

- **导航系统**: 底部标签栏、页面标题栏
- **用户系统**: 登录、注册、个人中心、资料管理
- **内容系统**: 车源列表、圈子动态、消息聊天
- **交互组件**: 按钮、输入框、图片、列表等
- **搜索功能**: 搜索框、搜索结果、搜索建议

通过这份文档，可以快速定位和修改任何界面元素或交互功能，提高开发效率。

## 🔧 最近更新

### 2025-08-28 更新

- ✅ **更新系统用户数据为真实用户**
  - 将 `src/config/im.ts` 中的测试用户数据替换为 3 个真实用户
  - 用户 1: user1 (1960535231902826498) - 测试账号一，手机号 15263215021
  - 用户 2: user2 (1960535362584756226) - 测试账号二，手机号 15263215022
  - 用户 3: user3 (1960545000583643138) - 测试 333，手机号 13333333333，有头像
  - 更新了对应的 userSig 和用户信息，确保 IM 功能正常工作
  - 更新了模拟好友申请数据，使用新的用户信息和真实时间戳
  - 修复了 TypeScript 类型错误，将 null 值改为 undefined
  - 修复了通讯录页面 API 404 错误，添加降级机制使用配置文件中的用户数据

### 2025-08-28 更新（之前）

- ✅ **修复 IM 登录重复初始化问题**
  - 简化 autoLoginIM 函数，避免重复调用 initTIM()
  - 直接在 autoLoginIM 中调用 tim.login()，而不是通过 loginTIM() 间接调用
  - 移除重复的初始化逻辑，确保 SDK 只初始化一次
  - 改进错误处理，IM 登录失败不影响主登录流程
  - 简化登录页面 IM 登录逻辑，使用延迟执行避免阻塞主登录
  - 减少 SDK 初始化等待时间，从 30 秒降到 10 秒
  - 直接使用固定的 SDKAppID: 1600101880，简化配置
- ✅ **简化 IM 登录逻辑，直接使用用户信息中的 imSign 字段**
  - 更新用户信息类型定义，添加 imSign 字段支持
  - 简化 autoLoginIM 函数，直接使用 userStore.userInfo.imSign 进行登录
  - 移除 getUserSig 函数的复杂逻辑，如果没有 imSign 则直接报错
  - 删除所有测试用户配置和相关的回退逻辑
  - 确保 IM 登录完全依赖服务器返回的 imSign 字段
- ✅ **清理项目中的所有测试页面**
  - 清理空的配置文件 `src/config/im.ts`，添加迁移说明
  - 移除聊天页面中的测试用户硬编码逻辑，改为使用 autoLoginIM
  - 删除未使用的函数和变量，修复 TypeScript 类型错误
  - 确保 IM 登录完全依赖服务器返回的 imSign 字段
- ✅ **升级聊天功能为腾讯云 IM SDK**
  - 将 `src/pages/chat/chat.vue` 聊天页面升级为使用腾讯云 IM SDK
  - 更新 `src/utils/im.ts` 为腾讯云 IM SDK 封装（替换原有的 tim-js-sdk）
  - 删除临时的 `src/utils/im-new.ts` 文件，功能已完全集成到 `src/utils/im.ts`
  - 保持原有 UI 样式不变，仅更新底层功能实现
  - 支持实时消息发送/接收、消息历史记录加载、消息状态管理
  - 集成自动重连机制和日志级别控制，减少控制台输出
  - 删除临时的 chat-new.vue 文件，功能已完全集成到 chat.vue
  - 更新聊天列表项跳转路径，确保点击进入升级后的聊天页面
- ✅ 优化用户详情页面跳转功能
  - 修改消息列表项 (`src/pages/contacts/components/messageItem.vue`) 支持点击头像和更多按钮跳转到用户详情页面
  - 点击用户头像或更多按钮跳转到真实的用户详情页面 (`/pages/user/profile`)
  - 点击消息内容区域仍然进入聊天界面，保持原有聊天功能
  - 群聊用户点击头像跳转到群聊信息页面 (`/pages/chat/group-info`)
  - 个人用户点击头像跳转到用户详情页面，显示真实用户信息和动态
  - 用户详情页面支持发消息和关注功能，提供完整的用户交互体验
  - 用户详情页面标题栏动态显示真实用户名，而不是固定的"用户详情"文字
  - 修复通讯录列表中用户 ID 被时间戳修改的问题，确保点击用户能正确跳转到真实用户详情页面
  - 修复用户详情页面"发消息"功能的参数传递问题，确保聊天页面和聊天设置页面显示真实用户信息
  - 重构聊天设置页面，移除所有测试代码，使用真实 API 接口获取用户信息和设置
  - 修复聊天页面消息显示问题：解决"未知消息"显示和消息位置错误的问题，优化消息排序逻辑
  - 深度修复消息内容解析问题：增强消息内容提取逻辑，修复时间戳显示错误，避免消息重复添加
  - 实现消息本地存储功能：创建 messageStorage 工具，支持消息本地缓存、离线查看、数据持久化
  - 简化消息处理逻辑：优化消息内容提取、修复滚动到底部功能、确保消息正确显示

### 2025-08-27 更新

- ✅ 简化 IM 登录逻辑，直接使用用户信息中的 imSign 字段
  - 更新用户信息类型定义，添加 imSign 字段支持
  - 简化 autoLoginIM 函数，直接使用 userStore.userInfo.imSign 进行登录
  - 移除 getUserSig 函数的复杂逻辑，如果没有 imSign 则直接报错
  - 删除所有测试用户配置和相关的回退逻辑
  - 确保 IM 登录完全依赖服务器返回的 imSign 字段
- ✅ 清理项目中的所有测试页面
  - 删除 IM 演示页面 (`src/pages/im-demo.vue`)
  - 删除 admin 目录下的所有测试页面 (`src/pages/admin/`)
  - 删除聊天测试页面 (`src/pages/chat/test.vue`)
  - 删除 test 目录下的所有测试页面 (`src/pages/test/`)
  - 删除 notifications 目录下的测试页面 (`src/pages/notifications/`)
  - 删除相关的 API 测试文件 (`src/api/blacklist.ts`, `src/api/groupMembers.ts`)
- ✅ 移除消息和通讯录中的新好友功能
  - 从通讯录页面移除新好友标签页
  - 删除新好友列表组件 (`src/pages/contacts/components/newFriendsList.vue`)
  - 更新通讯录页面，只保留消息和通讯录两个标签页
  - 清理相关的路由和组件引用
- ✅ 更新索引文档，移除已删除功能的映射关系
  - 移除测试页面的功能映射
  - 移除新好友功能的相关描述
  - 更新 API 接口映射，移除已删除的 API 服务
- ✅ 恢复系统通知和互动通知功能
  - 重新创建系统通知页面 (`src/pages/notifications/system.vue`) - 显示系统通知、订单通知、消息通知、退款通知
  - 重新创建互动通知页面 (`src/pages/notifications/interaction.vue`) - 显示关注、点赞、评论、分享等互动通知
  - 在消息列表页面添加通知入口，显示未读数量徽章
  - 支持筛选、标记已读、跳转详情等完整功能
  - 集成通知管理器，支持实时通知推送和状态管理
- ✅ 优化消息列表页面布局
  - 移除聊天功能入口区域，简化页面布局
  - 添加筛选标签栏（全部、未读）和一键已读功能
  - 保留系统通知和互动通知入口，突出通知功能
  - 优化页面结构，更符合实际使用场景
- ✅ 重新设计通知页面 UI
  - 系统通知页面：采用时间轴布局，每条通知显示时间、类型、内容和查看详情按钮
  - 互动通知页面：采用用户列表布局，显示用户头像、姓名、互动类型和关注按钮
  - 添加下拉筛选功能（新关注我的、收藏等），支持切换不同类型的互动通知
  - 优化页面样式，符合现代移动端设计规范
- ✅ 更新消息页面筛选栏样式
  - 将原有的筛选标签栏替换为"我的关注"标题和状态筛选
  - 添加关注数量显示：我的关注(数量)
  - 新增"全部"和"好友"状态切换按钮，采用圆角胶囊样式
  - 支持状态筛选功能，可根据不同状态加载对应数据

### 2025-08-26 更新

- ✅ 在聊天页面的更多功能面板中添加开发测试按钮
- ✅ 添加"个人设置"和"群聊信息"快速测试按钮（使用 emoji 图标）
- ✅ 实现测试按钮点击跳转到对应的聊天设置页面
- ✅ 恢复车源首页为原始状态，移除开发测试区域
- ✅ 更新路由配置：将 user-profile 替换为 chat/settings，group-info/group-info 替换为 chat/group-info
- ✅ 修改所有相关页面的路由跳转，统一使用新的路由路径
- ✅ 新增系统通知和互动通知功能
  - 在消息列表中添加系统通知和互动通知入口
  - 创建系统通知页面（系统消息、订单通知、退款通知等）
  - 创建互动通知页面（新关注、点赞、评论等）
  - 实现通知管理器，支持实时推送和未读数量统计
  - 支持通知的实时更新和已读状态管理
- ✅ 启用黑名单管理功能
  - 在聊天设置页面中启用黑名单管理入口
  - 实现 goToBlacklist 方法，支持跳转到黑名单管理页面
  - 黑名单管理页面已完整实现（用户列表、解除拉黑、时间显示等）
- ✅ 集成黑名单管理 API 接口
  - 创建黑名单 API 服务 (`src/api/blacklist.ts`)
  - 支持分页获取、添加、移除、批量操作等功能
  - 黑名单页面集成真实 API，支持加载状态和错误处理
  - API 失败时自动降级到模拟数据
- ✅ 集成群成员管理 API 接口
  - 创建群成员 API 服务 (`src/api/groupMembers.ts`)
  - 支持成员列表、添加、移除、角色管理、禁言等功能
  - 群成员页面集成真实 API，优先使用 API，失败时降级到 IM SDK
  - 支持搜索、分页、在线状态等高级功能
- ✅ 创建完整的 API 服务体系
  - 群组管理 API 服务 (`src/api/group.ts`) - 群组 CRUD、设置管理、成员管理等
  - IM 用户管理 API 服务 (`src/api/imUser.ts`) - 用户信息管理、状态控制、统计等
  - 用户通知 API 服务 (`src/api/userNotification.ts`) - 通知推送、已读管理、设置等
  - 会话管理 API 服务 (`src/api/conversation.ts`) - 会话 CRUD、置顶、免打扰、草稿等
  - 消息管理 API 服务 (`src/api/message.ts`) - 消息发送、撤回、已读、搜索、文件上传等
  - 用户关注管理 API 服务 (`src/api/userFollow.ts`) - 关注关系、分组管理、统计等
  - 支持完整的业务流程和数据管理
- ✅ 新增收藏功能页面
  - 创建收藏页面 (`src/pages/notifications/favorites.vue`) - 用户收藏车源的通知列表
  - 在互动通知页面添加收藏标签页，支持切换到收藏页面
  - 收藏页面支持用户头像、关注状态、收藏时间、车源缩略图等信息展示
  - 支持选中状态和跳转到车源详情功能
- ✅ 修复黑名单管理报错
  - 修复黑名单页面的 API 导入路径错误
  - 确保黑名单功能正常工作
- ✅ 优化通讯录真实用户数据
  - 移除模拟用户数据，使用真实的 IM 测试用户
  - 通讯录现在显示 3 个可登录 IM 的真实用户（administrator、牛牛 1、贝贝 1）
  - 支持真实的 IM 聊天和好友管理功能
- ✅ 创建完整 IM 功能测试清单
  - 新增测试文档 (`IM功能测试清单.md`) - 从头到尾的完整测试流程
  - 包含 6 个测试阶段：基础连接、用户管理、聊天功能、群聊功能、高级功能、API 接口
  - 提供详细的测试步骤、成功标准、注意事项和问题排查指南
- ✅ 更新索引文档，反映路由变更和新增功能
- ✅ 修复 IM 自动登录功能
  - 解决用户登录后 IM 未自动登录的问题
  - 修复 UserID 和 UserSig 不匹配的错误
  - 新增 `autoLoginIM()` 函数统一管理 IM 登录
  - 在用户登录成功后自动调用 IM 登录
  - 移除多个页面中冲突的 IM 初始化调用
  - 改进 UserSig 获取逻辑，支持测试用户映射
  - 创建专门的测试页面 (`src/pages/test/im-auto-login.vue`) 用于验证自动登录功能
  - 新增修复说明文档 (`IM自动登录修复说明.md`) 详细记录修复过程和使用方法

### 2025-01-27 更新

- ✅ 更新用户界面为真实用户数据
  - 修改用户详情页面 (`src/pages/user/profile.vue`) 使用真实用户数据
  - 修改聊天设置页面 (`src/pages/chat/settings.vue`) 使用真实用户数据
  - 修改通讯录列表 (`src/pages/contacts/components/contactsList.vue`) 使用真实用户数据
  - 修改消息列表 (`src/pages/contacts/components/messageList.vue`) 使用真实用户数据
- ✅ 更新群聊功能为 3 人真实群聊
  - 修改群聊信息页面 (`src/pages/chat/group-info.vue`) 使用 3 个真实用户作为群成员
  - 修改旧版群聊信息页面 (`src/pages/group-info/group-info.vue`) 使用真实用户数据
  - 更新消息列表中的群聊名称为"车友交流群(3)"和"二手车交易群(3)"
- ✅ 开始 API 接口路径标准化
  - 为部分 API 接口添加 `/app` 前缀（conversation.ts 已完成）
  - 修复 request 工具的导出方式，支持命名导出

### 2025-01-26 更新

- ✅ 在聊天页面导航栏添加设置按钮
- ✅ 实现聊天设置按钮点击路由功能（私聊跳转聊天设置，群聊跳转群聊信息）
- ✅ 新增聊天设置页面 (`src/pages/chat/settings.vue`)
  - 用户头像和基本信息展示
  - 免打扰设置、黑名单管理、备注名编辑
  - 清空聊天记录、投诉举报功能
- ✅ 新增群聊信息页面 (`src/pages/chat/group-info.vue`)
  - 群聊头像和基本信息展示
  - 群成员列表展示和管理
  - 群设置功能（群名称、群公告、消息免打扰、置顶聊天）
  - 危险操作（清空聊天记录、退出群聊）
- ✅ 更新路由配置，添加新页面路由
- ✅ 更新索引文档，添加新页面映射信息

### 2024-12-19 更新

- ✅ 修复 1 对 1 聊天界面导入错误
- ✅ 在关注列表中添加测试账号（管理员、牛牛 1、贝贝 1）
- ✅ 实现点击测试账号跳转到聊天页面功能
- ✅ 修复 IM 模块的默认导出问题
- ✅ 完善聊天页面参数接收和 IM 初始化逻辑
- ✅ 修复 getMessageList 函数返回类型
- ✅ 添加测试账号的 userID 和 userSig 字段支持
- ✅ 新增群聊创建页面 (`src/pages/chat/create-group.vue`)
- ✅ 新增群成员管理页面 (`src/pages/chat/group-members.vue`)
- ✅ 新增好友申请详情页面 (`src/pages/chat/friend-request.vue`)
- ✅ 新增消息转发页面 (`src/pages/chat/forward-message.vue`)
- ✅ 新增聊天文件管理页面 (`src/pages/chat/chat-files.vue`)
- ✅ 新增搜索聊天记录页面 (`src/pages/chat/search-messages.vue`)
- ✅ 完善聊天输入框功能（语音、表情、常用语、更多功能）

## 工具函数详细说明

### IM 相关工具 (`src/utils/im/`)

- `config.ts` - IM 配置文件，包含腾讯云 IM 的基础配置
- `index.ts` - IM 管理器，封装了腾讯云 IM SDK 的核心功能

### 消息存储工具 (`src/utils/messageStorage.ts`)

- 消息本地存储和管理工具
- **核心功能**：
  - `getConversationMessages()` - 获取指定会话的本地消息
  - `saveMessage()` - 保存单条消息到本地
  - `saveMessages()` - 批量保存消息
  - `markMessageAsRead()` - 标记消息为已读
  - `markConversationAsRead()` - 标记会话所有消息为已读
  - `deleteConversationMessages()` - 删除会话消息
  - `clearAllMessages()` - 清空所有本地消息
- **特性**：
  - 自动去重，避免重复存储
  - 按时间戳排序
  - 限制每个会话最多存储 1000 条消息
  - 支持离线查看历史消息
  - 与聊天页面无缝集成

### 2024-08-28 IM 功能整合与优化

- ✅ **整合重复的 IM 功能模块**：合并 `src/utils/im.ts` 和 `src/utils/im/index.ts`，统一使用 `src/utils/im/index.ts` 作为主要 IM 模块
- ✅ **统一 IM 配置文件**：将所有 IM 配置合并到 `src/config/im.ts`，删除重复的配置文件 `src/utils/im/config.ts`
- ✅ **更新通讯录真实用户数据**：通讯录现在使用真实 API 获取关注用户列表，不再显示测试用户
- ✅ **恢复通知页面功能**：重新创建 `src/pages/notifications/system.vue` 和 `src/pages/notifications/interaction.vue`，提供完整的通知管理功能
- ✅ **清理重复的 API 文件**：删除之前创建的重复 API 文件，避免功能冲突
- ✅ **修复所有 IM 导入问题**：
  - 聊天页面：`src/pages/chat/chat.vue` - 修复导入和方法调用
  - 聊天设置：`src/pages/chat/settings.vue` - 添加缺失的 computed 导入
  - 群聊创建：`src/pages/chat/create-group.vue` - 更新导入路径
  - 消息转发：`src/pages/chat/forward-message.vue` - 更新导入路径
  - 好友申请：`src/pages/chat/friend-request.vue` - 更新导入路径
  - 通讯录列表：`src/pages/contacts/components/contactsList.vue` - 使用真实 API
- ✅ **优化 IM 模块结构**：
  - 主要 IM 模块：`src/utils/im/index.ts` - 包含所有 IM 核心功能（已添加 sendMessage、logout 等方法）
  - 统一配置：`src/config/im.ts` - 包含 SDK 配置、事件类型、消息类型、用户数据等
  - 初始化工具：`src/utils/im/init.ts` - 应用启动时自动初始化 IM
- ✅ **代码整合效果**：
  - 减少了重复代码和配置文件
  - 统一了用户数据来源，确保数据一致性
  - 简化了 IM 功能的维护和扩展
  - 删除了不必要的测试页面和 API 文件
  - 修复了所有 404 导入错误

## 注意事项

1. 所有页面都需要在 `pages.json` 中注册
2. 新增的 API 接口需要在对应的 API 文件中定义
3. 页面间的数据传递使用 URL 参数或全局状态管理
4. 图片资源放在 `static/images/` 目录下
5. 样式使用 uni-app 的 rpx 单位，确保多端适配
6. **IM 功能统一使用 `src/utils/im/index.ts` 和 `src/config/im.ts`**
7. **通讯录用户数据统一从 `src/config/im.ts` 中的 `getAllTestUsers()` 获取**
