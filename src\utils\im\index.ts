/**
 * 腾讯云IM SDK 集成工具类
 * 提供即时通信功能：单聊、群聊、消息已读状态等
 */

import TencentCloudChat from '@tencentcloud/chat'
import TIMUploadPlugin from 'tim-upload-plugin'
import { IM_CONFIG } from '@/config/im'
import { ref } from 'vue'
import { useUserStore } from '@/stores/user'

export interface IMConfig {
  SDKAppID: number
  userID: string
  userSig: string
}

export interface Message {
  ID: string
  type: string
  payload: any
  from: string
  to: string
  time: number
  isRead: boolean
  conversationID: string
  conversationType: string
  nick?: string
  avatar?: string
}

export interface Conversation {
  conversationID: string
  type: string
  userProfile?: any
  groupProfile?: any
  lastMessage?: Message
  unreadCount: number
  isPinned: boolean
  muteNotifications: boolean
}

// IM状态
export const imState = ref({
  isLogin: false,
  isConnecting: false,
  userID: '',
  userSig: '',
  sdkReady: false,
  avatar: '',
  nickname: '',
})

class IMManager {
  private tim: any = null
  private isLoggedIn = false
  private isInitialized = false
  private messageListeners: Array<(message: Message) => void> = []
  private conversationListeners: Array<(conversations: Conversation[]) => void> = []
  private readReceiptListeners: Array<(data: any) => void> = []

  /**
   * 初始化IM SDK
   */
  async init(): Promise<boolean> {
    try {
      // 如果已经初始化，直接返回
      if (this.tim && this.isInitialized) {
        return true
      }

      console.log('开始初始化腾讯云IM SDK...')

      // 创建SDK实例
      const options = {
        SDKAppID: IM_CONFIG.SDKAppID,
      }

      this.tim = TencentCloudChat.create(options)
      console.log('腾讯云IM SDK实例创建成功')

      // 设置日志级别
      this.tim.setLogLevel(IM_CONFIG.LOG_LEVEL.ERROR)

      // 注册上传插件
      this.tim.registerPlugin({
        'tim-upload-plugin': TIMUploadPlugin,
      })

      // 绑定事件监听
      this.bindEvents()

      this.isInitialized = true
      console.log('IM SDK 初始化成功')
      return true
    } catch (error) {
      console.error('IM SDK 初始化失败:', error)
      return false
    }
  }

  /**
   * 自动登录IM（用于用户登录后自动登录IM）
   */
  async autoLogin(): Promise<boolean> {
    try {
      const userStore = useUserStore()

      console.log('开始IM自动登录检查...')
      console.log('用户登录状态:', userStore.isLogin)
      console.log('用户信息:', userStore.userInfo)

      // 检查用户是否已登录
      if (!userStore.isLogin) {
        console.log('用户未登录，跳过IM自动登录')
        return false
      }

      // 检查用户信息是否存在
      if (!userStore.userInfo) {
        console.log('用户信息不存在，跳过IM自动登录')
        return false
      }

      // 检查用户ID是否存在
      if (!userStore.userInfo.userId) {
        console.error('用户ID不存在，无法登录IM')
        console.log('用户信息详情:', JSON.stringify(userStore.userInfo, null, 2))
        return false
      }

      const userID = userStore.userInfo.userId
      console.log('准备登录IM，用户ID:', userID)

      // 检查是否有imSign
      if (!userStore.userInfo.imSign) {
        console.error('用户信息中没有imSign字段，无法登录IM')
        console.log('用户信息详情:', JSON.stringify(userStore.userInfo, null, 2))
        throw new Error('用户信息中缺少imSign字段')
      }

      console.log('imSign存在，长度:', userStore.userInfo.imSign.length)

      // 确保IM SDK已初始化
      if (!this.tim) {
        console.log('IM SDK未初始化，开始初始化...')
        const initSuccess = await this.init()
        if (!initSuccess) {
          console.error('IM SDK初始化失败')
          return false
        }
      }

      // 直接调用tim.login，避免重复初始化
      imState.value.isConnecting = true
      console.log('开始IM登录，用户ID:', userID)
      console.log('使用的imSign:', userStore.userInfo.imSign)
      console.log('imSign长度:', userStore.userInfo.imSign.length)

      const result = await this.tim.login({
        userID: userID,
        userSig: userStore.userInfo.imSign,
      })

      console.log('IM登录结果:', result)

      if (result.code === 0) {
        console.log('IM自动登录成功:', result)
        imState.value.isLogin = true
        imState.value.userID = userID
        imState.value.userSig = userStore.userInfo.imSign
        this.isLoggedIn = true
        return true
      } else {
        console.error('IM登录失败:', result.message)
        return false
      }
    } catch (error) {
      console.error('IM自动登录异常:', error)
      return false
    } finally {
      imState.value.isConnecting = false
    }
  }

  /**
   * 绑定IM事件监听
   */
  private bindEvents() {
    if (!this.tim) return

    // 监听消息接收
    this.tim.on(TencentCloudChat.EVENT.MESSAGE_RECEIVED, this.onMessageReceived.bind(this))

    // 监听会话列表更新
    this.tim.on(
      TencentCloudChat.EVENT.CONVERSATION_LIST_UPDATED,
      this.onConversationListUpdated.bind(this)
    )

    // 监听SDK状态变化
    this.tim.on(TencentCloudChat.EVENT.SDK_READY, this.onSDKReady.bind(this))
    this.tim.on(TencentCloudChat.EVENT.SDK_NOT_READY, this.onSDKNotReady.bind(this))

    // 监听被踢下线
    this.tim.on(TencentCloudChat.EVENT.KICKED_OUT, this.onKickedOut.bind(this))

    // 监听网络状态变化
    this.tim.on(TencentCloudChat.EVENT.NET_STATE_CHANGE, this.onNetStateChange.bind(this))
  }

  // 事件处理方法
  private onMessageReceived(event: any) {
    const messageList = event.data.map(this.formatMessage.bind(this))
    this.messageListeners.forEach((listener) => {
      messageList.forEach((message) => listener(message))
    })
  }

  private onConversationListUpdated(event: any) {
    const conversationList = event.data.map(this.formatConversation.bind(this))
    this.conversationListeners.forEach((listener) => listener(conversationList))
  }

  private onSDKReady() {
    console.log('IM SDK 准备就绪')
    imState.value.sdkReady = true
  }

  private onSDKNotReady() {
    console.log('IM SDK 未准备就绪')
    imState.value.sdkReady = false
  }

  private onKickedOut(event: any) {
    console.log('被踢下线:', event.data.type)
    this.isLoggedIn = false
    imState.value.isLogin = false
  }

  private onNetStateChange(event: any) {
    console.log('网络状态变化:', event.data.state)
  }

  // 格式化消息
  private formatMessage(message: any): Message {
    return {
      ID: message.ID,
      type: message.type,
      payload: message.payload,
      from: message.from,
      to: message.to,
      time: message.time,
      isRead: message.isRead || false,
      conversationID: message.conversationID,
      conversationType: message.conversationType,
      nick: message.nick,
      avatar: message.avatar,
    }
  }

  // 格式化会话
  private formatConversation(conversation: any): Conversation {
    return {
      conversationID: conversation.conversationID,
      type: conversation.type,
      userProfile: conversation.userProfile,
      groupProfile: conversation.groupProfile,
      lastMessage: conversation.lastMessage
        ? this.formatMessage(conversation.lastMessage)
        : undefined,
      unreadCount: conversation.unreadCount || 0,
      isPinned: conversation.isPinned || false,
      muteNotifications: conversation.muteNotifications || false,
    }
  }

  /**
   * 发送文本消息
   */
  async sendTextMessage(
    to: string,
    content: string,
    conversationType: string
  ): Promise<Message | null> {
    try {
      if (!this.tim || !this.isLoggedIn) {
        throw new Error('IM未登录')
      }

      const message = this.tim.createTextMessage({
        to,
        conversationType,
        payload: { text: content },
      })

      const result = await this.tim.sendMessage(message)
      if (result.code === 0) {
        return this.formatMessage(result.data.message)
      } else {
        throw new Error(`发送失败: ${result.message}`)
      }
    } catch (error) {
      console.error('发送消息失败:', error)
      return null
    }
  }

  /**
   * 获取会话列表
   */
  async getConversationList(): Promise<Conversation[]> {
    try {
      if (!this.tim || !this.isLoggedIn) {
        throw new Error('IM未登录')
      }

      const result = await this.tim.getConversationList()
      if (result.code === 0) {
        return result.data.conversationList.map(this.formatConversation.bind(this))
      } else {
        throw new Error(`获取会话列表失败: ${result.message}`)
      }
    } catch (error) {
      console.error('获取会话列表失败:', error)
      return []
    }
  }

  /**
   * 获取消息列表
   */
  async getMessageList(
    conversationId: string,
    nextReqMessageID?: string
  ): Promise<{ messageList: Message[]; nextReqMessageID: string; isCompleted: boolean }> {
    try {
      if (!this.tim || !this.isLoggedIn) {
        throw new Error('IM未登录')
      }

      const result = await this.tim.getMessageList({
        conversationID: conversationId,
        nextReqMessageID,
        count: 15,
      })

      if (result.code === 0) {
        return {
          messageList: result.data.messageList.map(this.formatMessage.bind(this)),
          nextReqMessageID: result.data.nextReqMessageID,
          isCompleted: result.data.isCompleted,
        }
      } else {
        throw new Error(`获取消息列表失败: ${result.message}`)
      }
    } catch (error) {
      console.error('获取消息列表失败:', error)
      return { messageList: [], nextReqMessageID: '', isCompleted: true }
    }
  }

  /**
   * 发送文本消息
   */
  async sendMessage(to: string, text: string, conversationType: string = 'C2C'): Promise<any> {
    try {
      if (!this.tim || !this.isLoggedIn) {
        throw new Error('IM未登录')
      }

      const message = this.tim.createTextMessage({
        to: to,
        conversationType:
          conversationType === 'GROUP'
            ? TencentCloudChat.TYPES.CONV_GROUP
            : TencentCloudChat.TYPES.CONV_C2C,
        payload: {
          text: text,
        },
      })

      const result = await this.tim.sendMessage(message)
      return result
    } catch (error) {
      console.error('发送消息失败:', error)
      throw error
    }
  }

  /**
   * 标记消息已读
   */
  async markMessageAsRead(conversationId: string): Promise<boolean> {
    try {
      if (!this.tim || !this.isLoggedIn) {
        throw new Error('IM未登录')
      }

      const result = await this.tim.setMessageRead({ conversationID: conversationId })
      if (result.code === 0) {
        return true
      } else {
        throw new Error(`标记已读失败: ${result.message}`)
      }
    } catch (error) {
      console.error('标记消息已读失败:', error)
      return false
    }
  }

  /**
   * 登出IM
   */
  async logout(): Promise<boolean> {
    try {
      if (!this.tim) {
        return true
      }

      const result = await this.tim.logout()

      if (result.code === 0) {
        imState.value.isLogin = false
        imState.value.userID = ''
        imState.value.userSig = ''
        imState.value.sdkReady = false
        this.isLoggedIn = false
        return true
      } else {
        console.error('IM登出失败:', result.message)
        return false
      }
    } catch (error) {
      console.error('IM登出异常:', error)
      return false
    }
  }

  /**
   * 添加消息监听器
   */
  addMessageListener(listener: (message: Message) => void) {
    this.messageListeners.push(listener)
  }

  /**
   * 移除消息监听器
   */
  removeMessageListener(listener: (message: Message) => void) {
    const index = this.messageListeners.indexOf(listener)
    if (index > -1) {
      this.messageListeners.splice(index, 1)
    }
  }

  /**
   * 添加会话监听器
   */
  addConversationListener(listener: (conversations: Conversation[]) => void) {
    this.conversationListeners.push(listener)
  }

  /**
   * 移除会话监听器
   */
  removeConversationListener(listener: (conversations: Conversation[]) => void) {
    const index = this.conversationListeners.indexOf(listener)
    if (index > -1) {
      this.conversationListeners.splice(index, 1)
    }
  }

  // Getter
  get isReady() {
    return this.isLoggedIn
  }

  get sdk() {
    return this.tim
  }
}

// 创建全局实例
const imManager = new IMManager()

// 导出实例
export default imManager
