<route lang="json">
{
  "style": {
    "navigationBarTitleText": "创建群聊",
    "navigationBarBackgroundColor": "#ffffff",
    "navigationBarTextStyle": "black"
  }
}
</route>

<template>
  <c-page>
    <view class="create-group-container">
      <!-- 群头像设置 -->
      <view class="avatar-section">
        <view class="avatar-wrapper" @click="selectGroupAvatar">
          <image :src="groupInfo.avatar" class="group-avatar" mode="aspectFill" />
          <view class="avatar-overlay">
            <image src="/static/images/user/camera.png" class="camera-icon" mode="aspectFit" />
          </view>
        </view>
        <text class="avatar-tip">点击设置群头像</text>
      </view>

      <!-- 群信息表单 -->
      <view class="form-section">
        <view class="form-item">
          <text class="form-label">群名称</text>
          <input
            v-model="groupInfo.name"
            class="form-input"
            placeholder="请输入群名称"
            maxlength="20"
          />
        </view>

        <view class="form-item">
          <text class="form-label">群简介</text>
          <textarea
            v-model="groupInfo.introduction"
            class="form-textarea"
            placeholder="请输入群简介（可选）"
            maxlength="100"
          />
        </view>
      </view>

      <!-- 选择群成员 -->
      <view class="members-section">
        <view class="section-header">
          <text class="section-title">选择群成员</text>
          <text class="member-count">已选择 {{ selectedMembers.length }} 人</text>
        </view>

        <!-- 已选成员展示 -->
        <view v-if="selectedMembers.length > 0" class="selected-members">
          <view
            v-for="member in selectedMembers"
            :key="member.userID"
            class="selected-member"
            @click="removeMember(member)"
          >
            <image :src="member.avatar" class="member-avatar" mode="aspectFill" />
            <text class="member-name">{{ member.nickname }}</text>
            <view class="remove-btn">×</view>
          </view>
        </view>

        <!-- 好友列表 -->
        <view class="friends-list">
          <view
            v-for="friend in friendList"
            :key="friend.userID"
            class="friend-item"
            :class="{ selected: isSelected(friend) }"
            @click="toggleMember(friend)"
          >
            <view class="friend-info">
              <image :src="friend.avatar" class="friend-avatar" mode="aspectFill" />
              <view class="friend-details">
                <text class="friend-name">{{ friend.nickname }}</text>
                <text class="friend-id">ID: {{ friend.userID }}</text>
              </view>
            </view>
            <view class="select-checkbox" :class="{ checked: isSelected(friend) }">
              <text v-if="isSelected(friend)" class="check-mark">✓</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 创建按钮 -->
      <view class="create-section">
        <view class="create-btn" :class="{ disabled: !canCreate }" @click="createGroup">
          <text class="create-text">创建群聊</text>
        </view>
      </view>
    </view>
  </c-page>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import imManager from '@/utils/im/index'
import { message } from '@/utils/util'

interface Friend {
  userID: string
  nickname: string
  avatar: string
}

interface GroupInfo {
  name: string
  avatar: string
  introduction: string
}

// 响应式数据
const groupInfo = reactive<GroupInfo>({
  name: '',
  avatar: '/static/images/user/tx.png',
  introduction: '',
})

const friendList = ref<Friend[]>([])
const selectedMembers = ref<Friend[]>([])
const isCreating = ref(false)

// 计算属性
const canCreate = computed(() => {
  return groupInfo.name.trim() && selectedMembers.value.length >= 2
})

// 生命周期
onMounted(() => {
  loadFriendList()
})

// 加载好友列表
const loadFriendList = async () => {
  try {
    const friends = await imManager.getFriendList()
    friendList.value = friends.map((friend: any) => ({
      userID: friend.userID,
      nickname: friend.nick || friend.userID,
      avatar: friend.avatar || '/static/images/user/tx.png',
    }))
  } catch (error) {
    console.error('加载好友列表失败:', error)
    message.error('加载好友列表失败')
  }
}

// 选择群头像
const selectGroupAvatar = () => {
  uni.chooseImage({
    count: 1,
    sizeType: ['compressed'],
    sourceType: ['album', 'camera'],
    success: (res) => {
      groupInfo.avatar = res.tempFilePaths[0]
    },
  })
}

// 检查成员是否已选择
const isSelected = (friend: Friend): boolean => {
  return selectedMembers.value.some((member) => member.userID === friend.userID)
}

// 切换成员选择状态
const toggleMember = (friend: Friend) => {
  if (isSelected(friend)) {
    removeMember(friend)
  } else {
    selectedMembers.value.push(friend)
  }
}

// 移除成员
const removeMember = (friend: Friend) => {
  const index = selectedMembers.value.findIndex((member) => member.userID === friend.userID)
  if (index > -1) {
    selectedMembers.value.splice(index, 1)
  }
}

// 创建群聊
const createGroup = async () => {
  if (!canCreate.value || isCreating.value) return

  isCreating.value = true

  try {
    const memberList = selectedMembers.value.map((member) => ({
      userID: member.userID,
    }))

    const groupData = await imManager.createGroup({
      name: groupInfo.name,
      type: 'Work', // 工作群
      memberList,
    })

    message.success('群聊创建成功')

    // 跳转到群聊页面
    uni.redirectTo({
      url: `/pages/chat/chat?groupId=${groupData.groupID}&groupName=${encodeURIComponent(
        groupInfo.name
      )}`,
    })
  } catch (error) {
    console.error('创建群聊失败:', error)
    message.error('创建群聊失败')
  } finally {
    isCreating.value = false
  }
}
</script>

<style scoped lang="scss">
.create-group-container {
  padding: 40rpx;
}

.avatar-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 60rpx;
}

.avatar-wrapper {
  position: relative;
  width: 160rpx;
  height: 160rpx;
  margin-bottom: 20rpx;
}

.group-avatar {
  width: 100%;
  height: 100%;
  border-radius: 20rpx;
  background-color: #f0f0f0;
}

.avatar-overlay {
  position: absolute;
  bottom: -10rpx;
  right: -10rpx;
  width: 60rpx;
  height: 60rpx;
  background-color: #1989fa;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.camera-icon {
  width: 32rpx;
  height: 32rpx;
}

.avatar-tip {
  font-size: 24rpx;
  color: #999;
}

.form-section {
  margin-bottom: 60rpx;
}

.form-item {
  margin-bottom: 40rpx;
}

.form-label {
  display: block;
  font-size: 32rpx;
  color: #333;
  margin-bottom: 20rpx;
}

.form-input {
  width: 100%;
  padding: 24rpx;
  border: 1rpx solid #e0e0e0;
  border-radius: 12rpx;
  font-size: 28rpx;
  background-color: #f8f8f8;
}

.form-textarea {
  width: 100%;
  min-height: 120rpx;
  padding: 24rpx;
  border: 1rpx solid #e0e0e0;
  border-radius: 12rpx;
  font-size: 28rpx;
  background-color: #f8f8f8;
}

.members-section {
  margin-bottom: 60rpx;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.section-title {
  font-size: 32rpx;
  color: #333;
  font-weight: bold;
}

.member-count {
  font-size: 24rpx;
  color: #1989fa;
}

.selected-members {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
  margin-bottom: 30rpx;
  padding: 20rpx;
  background-color: #f8f8f8;
  border-radius: 12rpx;
}

.selected-member {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 120rpx;
}

.member-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  margin-bottom: 10rpx;
}

.member-name {
  font-size: 20rpx;
  color: #666;
  text-align: center;
}

.remove-btn {
  position: absolute;
  top: -10rpx;
  right: 10rpx;
  width: 32rpx;
  height: 32rpx;
  background-color: #ff4757;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 20rpx;
}

.friends-list {
  max-height: 600rpx;
  overflow-y: auto;
}

.friend-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx;
  border-bottom: 1rpx solid #f0f0f0;
  transition: background-color 0.2s;
}

.friend-item:active {
  background-color: #f8f8f8;
}

.friend-item.selected {
  background-color: #e8f4ff;
}

.friend-info {
  display: flex;
  align-items: center;
  flex: 1;
}

.friend-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  margin-right: 24rpx;
}

.friend-details {
  flex: 1;
}

.friend-name {
  display: block;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 8rpx;
}

.friend-id {
  font-size: 24rpx;
  color: #999;
}

.select-checkbox {
  width: 40rpx;
  height: 40rpx;
  border: 2rpx solid #ddd;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
}

.select-checkbox.checked {
  background-color: #1989fa;
  border-color: #1989fa;
}

.check-mark {
  color: white;
  font-size: 24rpx;
  font-weight: bold;
}

.create-section {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 40rpx;
  background-color: white;
  border-top: 1rpx solid #f0f0f0;
}

.create-btn {
  width: 100%;
  padding: 32rpx;
  background-color: #1989fa;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s;
}

.create-btn.disabled {
  background-color: #ccc;
}

.create-text {
  color: white;
  font-size: 32rpx;
  font-weight: bold;
}
</style>
