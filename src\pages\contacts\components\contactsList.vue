<template>
  <view class="chat-list bg-white relative z-10 rounded-t-[30rpx] overflow-scroll p-[20rpx]">
    <!-- 我的关注标题和筛选 -->
    <view class="flex-row items-center justify-between mb-[24rpx]">
      <view class="flex-row h-[56rpx] items-center justify-center">
        <text class="ml-[10rpx] text-[30rpx] text-title">
          我的关注({{ state.dataList.length }})
        </text>
      </view>
      <view class="flex-row h-[66rpx] items-center p-[6rpx] rounded-[24rpx] bg-[#F4F6FA]">
        <view
          class="justify-center items-center w-[96rpx] h-[54rpx] rounded-[20rpx]"
          :class="[state.queryForm.status === 'all' ? 'bg-white' : '']"
          @click="switchStatus('all')"
        >
          <text
            class="text-[28rpx]"
            :class="[state.queryForm.status === 'all' ? 'bg-white text-title' : 'text-muted']"
            >全部</text
          >
        </view>
        <view
          class="justify-center items-center w-[96rpx] h-[54rpx] rounded-[20rpx]"
          :class="[state.queryForm.status === 'friend' ? 'bg-white' : '']"
          @click="switchStatus('friend')"
        >
          <text
            class="text-[28rpx]"
            :class="[state.queryForm.status === 'friend' ? 'bg-white text-title' : 'text-muted']"
            >好友</text
          >
        </view>
      </view>
    </view>

    <!-- 联系人列表 -->
    <contactItem
      v-for="item in state.dataList"
      :key="item.listKey || item.id"
      :data="item"
    ></contactItem>
    <c-loadmore :status="state.loadStatus" @reload="getData()"></c-loadmore>
  </view>
</template>

<script setup lang="ts">
import { reactive, onMounted, onUnmounted } from 'vue'
import contactItem from './contactItem.vue'
import { cloneDeep } from 'lodash-es'
import dayjs from 'dayjs'
import { getUserFollowings } from '@/api/userFollow'
import { useUserStore } from '@/stores/user'

interface IContact {
  id: string
  avatar: string
  username: string
  remark?: string
  isFriend: boolean
  listKey?: string // 用于列表渲染的唯一key
}

type Status = 'all' | 'friend'

const state = reactive({
  loading: false,
  loadStatus: 'loadend',
  queryForm: {
    current: 1,
    size: 20,
    status: 'all' as Status,
  },
  dataList: [] as IContact[],
})

// 真实用户数据
let realUsersData: IContact[] = []
const userStore = useUserStore()

const getData = async function () {
  state.loadStatus = 'loading'

  try {
    // 获取真实的关注用户列表
    const currentUserId = userStore.userInfo?.userId
    if (!currentUserId) {
      throw new Error('用户未登录')
    }

    try {
      const response = await getUserFollowings(currentUserId)
      const followings = Array.isArray(response.data) ? response.data : []

      realUsersData = followings.map((user: any) => ({
        id: user.userId || user.id,
        avatar: user.avatar || '/static/images/user/default_avatar.png',
        username: user.nickname || user.username || '未知用户',
        isFriend: true, // 关注的用户都标记为好友
      }))
    } catch (apiError) {
      console.warn('API获取关注列表失败，使用配置文件中的测试用户:', apiError)
      // API失败时，使用配置文件中的测试用户数据
      const { getAllTestUsers } = await import('@/config/im')
      const testUsers = getAllTestUsers()

      realUsersData = testUsers.map((user) => ({
        id: user.userID,
        avatar: user.avatar
          ? `/static/images/user/${user.avatar}`
          : '/static/images/user/default_avatar.png',
        username: user.nickname,
        isFriend: true,
      }))
    }

    let _dataList: IContact[] = cloneDeep(realUsersData)

    // 根据状态筛选数据
    if (state.queryForm.status === 'friend') {
      _dataList = _dataList.filter((item) => item.isFriend)
    }

    state.dataList.push(
      ..._dataList.map((item, index) => {
        // 保持原始用户ID不变，使用索引和时间戳作为列表项的唯一key
        return {
          ...item,
          listKey: item.id + '_' + dayjs().valueOf() + '_' + index,
        }
      })
    )
    state.loadStatus = 'loadend'
    uni.stopPullDownRefresh()
  } catch (error) {
    console.error('获取联系人失败:', error)
    state.loadStatus = 'loadend'
    uni.stopPullDownRefresh()
    // 如果API失败，使用空数据
    realUsersData = []
    let _dataList: IContact[] = []

    // 根据状态筛选数据
    if (state.queryForm.status === 'friend') {
      _dataList = _dataList.filter((item) => item.isFriend)
    }

    state.dataList.push(
      ..._dataList.map((item, index) => {
        return {
          ...item,
          id: `${item.id}_${index}_${Date.now()}`, // 确保唯一性
        }
      })
    )
  }
}

const initData = function () {
  state.queryForm.current = 1
  state.dataList = []
  getData()
}

const loadMore = function () {
  state.queryForm.current++
  getData()
}

const switchStatus = function (status: Status) {
  state.queryForm.status = status
  initData()
}

// 检查IM状态
const checkIMStatus = () => {
  // 不再自动初始化IM，避免与自动登录冲突
}

// 组件挂载时检查状态
onMounted(() => {
  checkIMStatus()
  getData() // 初始化数据
})

// 组件卸载时清理
onUnmounted(() => {
  // 清理监听器等
})

// 初始化数据
initData()

defineExpose({
  loadMore,
  getData,
  initData,
})
</script>

<style scoped lang="scss"></style>
