/**
 * IM用户管理API
 */
import request from '@/utils/request'

export interface ImUser {
  id: string
  userId: string
  nickname: string
  avatar?: string
  signature?: string
  gender?: 'male' | 'female' | 'unknown'
  birthday?: string
  location?: string
  language?: string
  status: 'active' | 'inactive' | 'banned'
  isOnline: boolean
  lastLoginTime?: string
  createTime: string
  updateTime?: string
  remarkName?: string
  isVerified?: boolean
  level?: number
  tags?: string[]
}

export interface ImUserResponse {
  code: number
  message: string
  data: ImUser
}

export interface ImUserListResponse {
  code: number
  message: string
  data: ImUser[]
  total?: number
}

export interface ImUserPageParams {
  current?: number
  size?: number
  keyword?: string
  status?: string
  isOnline?: boolean
}

/**
 * 修改用户关联IM用户
 */
export const updateImUser = (userId: string, data: Partial<ImUser>) => {
  return request.put({
    url: '/app/imUser',
    data: {
      userId,
      ...data,
    },
  })
}

/**
 * 新增用户关联IM用户
 */
export const createImUser = (data: {
  userId: string
  nickname: string
  avatar?: string
  signature?: string
  gender?: string
  birthday?: string
  location?: string
  language?: string
}) => {
  return request.post({
    url: '/app/imUser',
    data,
  })
}

/**
 * 通过id删除用户关联IM用户
 */
export const deleteImUser = (userId: string) => {
  return request.delete({
    url: '/app/imUser',
    data: {
      userId,
    },
  })
}

/**
 * 批量导入IM用户
 */
export const importImUsers = (users: Array<Partial<ImUser>>) => {
  return request<{ code: number; message: string }>({
    url: '/app/imUser/import',
    method: 'POST',
    data: {
      users,
    },
  })
}

/**
 * 分页查询IM用户
 */
export const getImUsersPage = (params: ImUserPageParams = {}) => {
  return request<ImUserListResponse>({
    url: '/app/imUser/page',
    method: 'GET',
    params: {
      current: params.current || 1,
      size: params.size || 20,
      keyword: params.keyword,
      status: params.status,
      isOnline: params.isOnline,
    },
  })
}

/**
 * 导出IM用户列表
 */
export const exportImUsers = () => {
  return request<Blob>({
    url: '/app/imUser/export',
    method: 'GET',
    responseType: 'blob',
  })
}

/**
 * 通过id查询IM用户详情
 */
export const getImUserDetails = (userId: string) => {
  return request.get({
    url: '/app/imUser/details',
    data: {
      userId,
    },
  })
}

/**
 * 获取用户在线状态
 */
export const getUserOnlineStatus = (userIds: string[]) => {
  return request<{
    code: number
    message: string
    data: Array<{ userId: string; isOnline: boolean; lastActiveTime?: string }>
  }>({
    url: '/app/imUser/online-status',
    method: 'POST',
    data: {
      userIds,
    },
  })
}

/**
 * 设置用户状态
 */
export const updateUserStatus = (userId: string, status: 'active' | 'inactive' | 'banned') => {
  return request<{ code: number; message: string }>({
    url: '/app/imUser/status',
    method: 'PUT',
    data: {
      userId,
      status,
    },
  })
}

/**
 * 批量设置用户状态
 */
export const batchUpdateUserStatus = (
  userIds: string[],
  status: 'active' | 'inactive' | 'banned'
) => {
  return request<{ code: number; message: string }>({
    url: '/app/imUser/batch-status',
    method: 'PUT',
    data: {
      userIds,
      status,
    },
  })
}

/**
 * 搜索IM用户
 */
export const searchImUsers = (keyword: string, limit?: number) => {
  return request<ImUserListResponse>({
    url: '/app/imUser/search',
    method: 'GET',
    params: {
      keyword,
      limit: limit || 20,
    },
  })
}

/**
 * 重置用户密码/重新生成UserSig
 */
export const resetUserCredentials = (userId: string) => {
  return request<{
    code: number
    message: string
    data: {
      userSig: string
      expireTime: number
    }
  }>({
    url: '/app/imUser/reset-credentials',
    method: 'POST',
    data: {
      userId,
    },
  })
}

/**
 * 获取用户的好友列表
 */
export const getUserFriends = (userId: string) => {
  return request<{
    code: number
    message: string
    data: Array<{
      friendId: string
      nickname: string
      avatar?: string
      remarkName?: string
      addTime: string
    }>
  }>({
    url: '/app/imUser/friends',
    method: 'GET',
    params: {
      userId,
    },
  })
}

/**
 * 获取用户的群组列表
 */
export const getUserGroups = (userId: string) => {
  return request<{
    code: number
    message: string
    data: Array<{
      groupId: string
      groupName: string
      role: 'owner' | 'admin' | 'member'
      joinTime: string
    }>
  }>({
    url: '/app/imUser/groups',
    method: 'GET',
    params: {
      userId,
    },
  })
}

/**
 * 强制用户下线
 */
export const forceUserOffline = (userId: string) => {
  return request<{ code: number; message: string }>({
    url: '/app/imUser/force-offline',
    method: 'POST',
    data: {
      userId,
    },
  })
}

/**
 * 发送系统消息给用户
 */
export const sendSystemMessage = (userId: string, message: string, messageType?: string) => {
  return request<{ code: number; message: string }>({
    url: '/app/imUser/system-message',
    method: 'POST',
    data: {
      userId,
      message,
      messageType: messageType || 'text',
    },
  })
}

/**
 * 批量发送系统消息
 */
export const batchSendSystemMessage = (
  userIds: string[],
  message: string,
  messageType?: string
) => {
  return request<{ code: number; message: string }>({
    url: '/app/imUser/batch-system-message',
    method: 'POST',
    data: {
      userIds,
      message,
      messageType: messageType || 'text',
    },
  })
}
