<route lang="json">
{
  "style": {
    "navigationBarTitleText": "登录",
    "navigationStyle": "custom"
  },
  "auth": false
}
</route>
<template>
  <c-page fullScreen>
    <image
      class="absolute left-0 top-0 w-full z-0"
      :src="$imgUrl('/statics/user/login_bg.png')"
      mode="widthFix"
    ></image>
    <view class="sticky z-10 top-0">
      <c-titlebar
        showBack
        :title="
          loginWay == LoginWayEnum.MOBILE
            ? '验证码登录'
            : loginWay == LoginWayEnum.ACCOUNT
            ? '密码登录'
            : ''
        "
      ></c-titlebar>
    </view>
    <view class="w-full relative z-10 px-2xl">
      <view class="mt-5xl">
        <view class="mb-mn">
          <text class="text-[48rpx] font-medium">您好！ </text>
        </view>
        <view class="mb-base">
          <text class="text-[48rpx] font-medium">欢迎登录车富强</text>
        </view>
        <view class="mb-3xl">
          <text class="text-secondary">未注册的手机验证后自动创建账号</text>
        </view>
      </view>
      <view class="mt-lg">
        <!-- 验证码登录 -->
        <view v-if="loginWay == LoginWayEnum.MOBILE">
          <view class="form-cell flex-row items-center mb-[30rpx]">
            <input
              type="number"
              v-model="formData.mobile"
              class="flex-1"
              placeholder="请输入手机号"
            />
            <!-- #ifdef MP-WEIXIN -->
            <view
              class="border-l border-solid border-0 border-light pl-3 text-muted leading-4 ml-3 w-[200rpx]"
            >
              <button
                class="mini-btn"
                open-type="getPhoneNumber"
                @getphonenumber="onGetWxPhoneNumber"
              >
                <text class="text-[#576B95]"> 获取手机号 </text>
              </button>
              <!-- <view class="mini-btn">获取手机号</view> -->
            </view>
            <!-- #endif -->
          </view>
          <view class="form-cell flex-row items-center mb-[30rpx]">
            <input
              type="number"
              v-model="formData.code"
              class="flex-1"
              placeholder="请输入验证码"
            />
            <view class="pl-3 leading-4 ml-3" @click="sendSms">
              <text class="mini-btn text-[#576B95]">{{
                smsState.currentTime >= 0 ? `${smsState.currentTime}s重新获取` : '获取验证码'
              }}</text>
            </view>
          </view>
        </view>
        <!-- END验证码登录 -->
        <!-- 密码登录 -->
        <view v-if="loginWay == LoginWayEnum.ACCOUNT">
          <view class="form-cell flex-row items-center mb-[30rpx]">
            <input
              type="text"
              v-model="formData.username"
              class="flex-1"
              placeholder="请输入手机号/账号"
            />
          </view>
          <view class="form-cell flex-row items-center mb-[30rpx]">
            <input
              type="text"
              v-model="formData.password"
              class="flex-1"
              placeholder="请输入密码"
            />
          </view>
        </view>
      </view>
      <!-- END密码登录 -->

      <view class="text-content flex-row justify-between mb-sm">
        <view class="flex-1">
          <view
            class="flex-row"
            v-if="loginWay == LoginWayEnum.MOBILE"
            @click="changeLoginWay(LoginTypeEnum.ACCOUNT, LoginWayEnum.ACCOUNT)"
          >
            <view class="mr-xs">
              <c-icon type="qiehuan" color="#576B95"></c-icon>
            </view>
            <text class="text-[#576B95]">密码登录</text>
          </view>
          <view
            class="flex-row"
            v-if="loginWay == LoginWayEnum.ACCOUNT"
            @click="changeLoginWay(LoginTypeEnum.MOBILE, LoginWayEnum.MOBILE)"
          >
            <view class="mr-xs">
              <c-icon type="qiehuan" color="#576B95"></c-icon>
            </view>
            <text class="text-[#576B95]">验证码登录</text>
          </view>
        </view>

        <navigator
          url="/user/resetPwd"
          hover-class="none"
          class="text-sm transition-colors duration-300 text-[#576B95]"
        >
          找回密码
        </navigator>
      </view>

      <view class="w-full mt-5xl">
        <c-button
          type="primary"
          block
          size="large"
          @click="handleLogin(loginWay === 1 ? 'account' : 'mobile')"
        >
          立即登录
        </c-button>
      </view>
      <!-- #ifdef MP-WEIXIN || H5 || APP -->
      <view class="mt-[80rpx] items-center justify-center">
        <c-button type="success" circle iconOnly size="large" icon="wechat" @click="wxLogin">
        </c-button>
        <view class="mt-[24rpx]">
          <text class="text-muted">微信登录</text>
        </view>
      </view>
      <!-- #endif -->
      <view class="flex-row text-sm items-center py-[24rpx]">
        <radio
          class="radio"
          @click="isCheckAgreement = !isCheckAgreement"
          color="#0767FF"
          :checked="isCheckAgreement"
        />
        <view class="flex-row text-sm items-center" @click="isCheckAgreement = !isCheckAgreement">
          <text class="text-secondary">已阅读并同意</text>
          <view @click.stop>
            <navigator hover-class="none" url="/pages/agreement/agreement?type=service">
              <text class="text-[#576B95]">《服务协议》</text>
            </navigator>
          </view>
          <text class="text-secondary">和</text>
          <view @click.stop>
            <navigator hover-class="none" url="/pages/agreement/agreement?type=privacy">
              <text class="text-[#576B95]">《隐私协议》</text>
            </navigator>
          </view>
        </view>
      </view>
    </view>
  </c-page>
</template>

<script setup lang="ts">
import { mobileLogin, accountLogin, mnpLogin, OALogin } from '@/api/account'
import { smsSend } from '@/api/app'
import { getUserCenter, userEdit } from '@/api/user'
// import { BACK_URL } from '@/enums/cacheEnums'
import { useLockFn } from '@/hooks/useLockFn'
import { useUserStore } from '@/stores/user'
// #ifdef H5
import wechatOa from '@/utils/wechat'
// #endif
import { isWeixinClient } from '@/utils/client'
import { onLoad, onShow } from '@dcloudio/uni-app'
import { reactive, ref } from 'vue'
import { message } from '@/utils/util'
enum LoginTypeEnum {
  MOBILE = 'mobile',
  ACCOUNT = 'account',
}

enum LoginWayEnum {
  ACCOUNT = 1,
  MOBILE = 2,
}

const isWeixin = ref(true)
const showLoginPopup = ref(false)
// #ifdef H5
isWeixin.value = isWeixinClient()
// #endif

const userStore = useUserStore()

const loginWay = ref<LoginWayEnum>(LoginWayEnum.ACCOUNT)
const isCheckAgreement = ref(false)

const loginData = ref<any>({})
const formData = reactive({
  scene: '',
  username: '',
  password: '',
  code: '',
  mobile: '',
})

// #ifdef MP-WEIXIN
const onGetWxPhoneNumber = function (e) {
  console.log(e)
  const { code } = e.detail
  console.log(code)
}
// #ENDIF

/**
 * 发送验证码
 */
const smsState = reactive({
  maxTime: 60,
  currentTime: -1,
  timer: null as any,
})
const sendSms = async () => {
  if (smsState.currentTime >= 0) {
    return
  }
  if (!formData.mobile || formData.mobile.length !== 11) {
    message.warning('手机号不合法')
    return
  }
  console.log('开始发送')
  await smsSend(formData.mobile)
  smsState.currentTime = smsState.maxTime
  message.warning('发送成功')
  smsState.timer = setInterval(() => {
    if (smsState.currentTime <= 0) {
      smsState.currentTime = -1
      clearInterval(smsState.timer)
      return
    }
    smsState.currentTime--
  }, 1000)
}

onUnmounted(() => {
  clearInterval(smsState.timer)
})

const changeLoginWay = (type: LoginTypeEnum, way: LoginWayEnum) => {
  formData.scene = type
  loginWay.value = way
}

const loginFun = async (scene: LoginTypeEnum) => {
  try {
    await checkAgreement()
    if (scene == LoginTypeEnum.ACCOUNT) {
      if (!formData.username) return message.warning('请输入账号/手机号码')
      if (!formData.password) return message.warning('请输入密码')
    }
    if (scene == LoginTypeEnum.MOBILE) {
      if (!formData.mobile) return message.warning('请输入手机号码')
      if (!formData.code) return message.warning('请输入验证码')
    }
    message.loading('登录中...')

    let data
    switch (scene) {
      case LoginTypeEnum.MOBILE:
        data = await mobileLogin(formData)
        break
      default:
        data = await accountLogin(formData)
        break
    }

    if (data) {
      loginHandle(data)
    }
  } catch (error: any) {
    uni.hideLoading()
    message.warning(error.message)
  }
}

const loginHandle = async (data: any) => {
  const { access_token } = data
  userStore.login(access_token)
  await userStore.getUser()

  // 简化IM登录 - 不阻塞主登录流程
  setTimeout(async () => {
    try {
      console.log('开始IM登录...')
      const imManager = await import('@/utils/im/index')
      await imManager.default.autoLogin()
    } catch (error) {
      console.log('IM登录失败，不影响主要功能:', error)
    }
  }, 1000) // 延迟1秒执行，避免阻塞主登录

  message.warning('登录成功')
  uni.hideLoading()
  uni.reLaunch({
    url: '/pages/index/index',
  })
}

const { lockFn: handleLogin } = useLockFn(loginFun)

const checkAgreement = async () => {
  if (!isCheckAgreement.value) throw new Error('请勾选底部已阅读并同意《服务协议》和《隐私协议》')
}
const { lockFn: wxLogin } = useLockFn(async () => {
  try {
    await checkAgreement()
    // #ifdef MP-WEIXIN || APP
    message.loading('登录中...')
    const { code }: any = await uni.login({
      provider: 'weixin',
    })
    const data = await mnpLogin(code)
    loginData.value = data
    const { access_token } = data

    // 临时用户没有昵称
    userStore.login(access_token)
    const res = await getUserCenter()
    if (!res.data.appUser.nickname) {
      uni.hideLoading()
      userStore.temToken = access_token
      showLoginPopup.value = true
      return
    }
    loginHandle(data)
    // #endif
    // #ifdef H5
    if (isWeixin.value) {
      wechatOa.getUrl()
    }
    // #endif
  } catch (error: any) {
    message.warning(error?.message)
  }
})

const handleUpdateUser = async (value: any) => {
  await userEdit(value)
  showLoginPopup.value = false
  loginHandle(loginData.value)
}

onShow(async () => {
  try {
    if (userStore.isLogin) {
      uni.showLoading({
        title: '请稍后...',
      })
      await userStore.getUser()
      uni.hideLoading()
      uni.navigateBack()
    }
  } catch (error: any) {
    uni.hideLoading()
  }
})

onLoad(async (options: any) => {
  // 清空用户信息
  userStore.logout()

  if (userStore.isLogin) {
    // 已经登录 => 首页
    uni.reLaunch({
      url: '/pages/index/index',
    })
    return
  }

  // #ifdef H5
  const { code, state } = options
  if (!isWeixin.value) return
  if (code) {
    const data = await OALogin(code)

    if (data) {
      loginHandle(data)
    }
  }
  // #endif
})
</script>

<style lang="scss">
.form-cell {
  height: 82rpx;
  padding: 12rpx 20rpx;
  border-radius: 20rpx;
  background: #f8f8f8;
  input {
    height: 100%;
  }
}
.radio {
  transform: scale(0.7);
}
</style>
