/**
 * 用户关注管理API
 */
import request from '@/utils/request'

export interface UserFollow {
  id: string
  userId: string
  followUserId: string
  followUserName: string
  followUserAvatar?: string
  remarkName?: string
  isDoNotDisturb: boolean
  isBlocked: boolean
  followTime: string
  updateTime?: string
  status: 'active' | 'cancelled' | 'blocked'
  isMutualFollow: boolean
  groupId?: string // 分组ID
  groupName?: string // 分组名称
  tags?: string[] // 标签
}

export interface UserFollowResponse {
  code: number
  message: string
  data: UserFollow
}

export interface UserFollowListResponse {
  code: number
  message: string
  data: UserFollow[]
  total?: number
}

export interface UserFollowPageParams {
  current?: number
  size?: number
  userId?: string
  followUserId?: string
  keyword?: string
  status?: string
  groupId?: string
  isDoNotDisturb?: boolean
  isMutualFollow?: boolean
}

export interface FollowGroup {
  id: string
  userId: string
  groupName: string
  groupOrder: number
  memberCount: number
  createTime: string
}

/**
 * 修改用户关注
 */
export const updateUserFollow = (followId: string, data: Partial<UserFollow>) => {
  return request.put<{ code: number; message: string }>({
    url: '/app/imUserFollow',
    data: {
      id: followId,
      ...data,
    },
  })
}

/**
 * 通过id删除用户关注
 */
export const deleteUserFollow = (followId: string) => {
  return request.delete<{ code: number; message: string }>({
    url: '/app/imUserFollow',
    data: {
      id: followId,
    },
  })
}

/**
 * 修改好友关注
 */
export const updateFollowRemarkName = (
  userId: string,
  followUserId: string,
  remarkName: string
) => {
  return request.post<{ code: number; message: string }>({
    url: '/app/imUserFollow/updateRemarkName',
    data: {
      userId,
      followUserId,
      remarkName,
    },
  })
}

/**
 * 好友关注设置免打扰
 */
export const setFollowDoNotDisturb = (
  userId: string,
  followUserId: string,
  doNotDisturb: boolean
) => {
  return request.post<{ code: number; message: string }>({
    url: '/app/imUserFollow/noDisturbingConfig',
    data: {
      userId,
      followUserId,
      doNotDisturb,
    },
  })
}

/**
 * 批量导入关注
 */
export const importUserFollows = (follows: Array<Partial<UserFollow>>) => {
  return request.post<{ code: number; message: string }>({
    url: '/app/imUserFollow/import',
    data: {
      follows,
    },
  })
}

/**
 * 取消用户关注
 */
export const cancelUserFollow = (userId: string, followUserId: string) => {
  return request.post<{ code: number; message: string }>({
    url: '/app/imUserFollow/imUserFollowCancel',
    data: {
      userId,
      followUserId,
    },
  })
}

/**
 * 新增用户关注
 */
export const addUserFollow = (
  userId: string,
  followUserId: string,
  remarkName?: string,
  groupId?: string
) => {
  return request.post<{ code: number; message: string }>({
    url: '/app/imUserFollow/imUserFollowAdd',
    data: {
      userId,
      followUserId,
      remarkName,
      groupId,
    },
  })
}

/**
 * 好友朋友关闭设置免打扰
 */
export const cancelFollowDoNotDisturb = (userId: string, followUserId: string) => {
  return request.post<{ code: number; message: string }>({
    url: '/app/imUserFollow/cancelNoDisturbingConfig',
    data: {
      userId,
      followUserId,
    },
  })
}

/**
 * 分页查询用户关注表
 */
export const getUserFollowsPage = (params: UserFollowPageParams = {}) => {
  const queryParams = new URLSearchParams({
    current: String(params.current || 1),
    size: String(params.size || 20),
    ...(params.userId && { userId: params.userId }),
    ...(params.followUserId && { followUserId: params.followUserId }),
    ...(params.keyword && { keyword: params.keyword }),
    ...(params.status && { status: params.status }),
    ...(params.groupId && { groupId: params.groupId }),
    ...(params.isDoNotDisturb !== undefined && { isDoNotDisturb: String(params.isDoNotDisturb) }),
    ...(params.isMutualFollow !== undefined && { isMutualFollow: String(params.isMutualFollow) }),
  }).toString()

  return request.get<UserFollowListResponse>({
    url: `/app/imUserFollow/page?${queryParams}`,
  })
}

/**
 * 查询好友朋友设置
 */
export const getFriendsConfig = (userId: string, followUserId: string) => {
  return request.get<{
    code: number
    message: string
    data: {
      remarkName?: string
      isDoNotDisturb: boolean
      groupId?: string
      groupName?: string
      tags?: string[]
      isMutualFollow: boolean
    }
  }>({
    url: `/app/imUserFollow/getFriendsConfig?userId=${userId}&followUserId=${followUserId}`,
  })
}

/**
 * 导出关注列表
 */
export const exportUserFollows = (params?: UserFollowPageParams) => {
  const queryParams = params
    ? new URLSearchParams(
        Object.entries(params).reduce((acc, [key, value]) => {
          if (value !== undefined) {
            acc[key] = String(value)
          }
          return acc
        }, {} as Record<string, string>)
      ).toString()
    : ''

  return request.get<Blob>({
    url: `/app/imUserFollow/export${queryParams ? `?${queryParams}` : ''}`,
  })
}

/**
 * 通过条件查询关注详情
 */
export const getUserFollowDetails = (followId: string) => {
  return request.get<UserFollowResponse>({
    url: `/app/imUserFollow/details?id=${followId}`,
  })
}

/**
 * 获取用户的关注列表
 */
export const getUserFollowings = (userId: string) => {
  return request.get<UserFollowListResponse>({
    url: `/app/imUserFollow/followings?userId=${userId}`,
  })
}

/**
 * 获取用户的粉丝列表
 */
export const getUserFollowers = (userId: string) => {
  return request.get<UserFollowListResponse>({
    url: `/app/imUserFollow/followers?userId=${userId}`,
  })
}

/**
 * 获取互相关注的好友列表
 */
export const getMutualFollows = (userId: string) => {
  return request.get<UserFollowListResponse>({
    url: '/app/imUserFollow/mutual-follows',
    data: {
      userId,
    },
  })
}

/**
 * 检查关注关系
 */
export const checkFollowRelation = (userId: string, targetUserId: string) => {
  return request.get<{
    code: number
    message: string
    data: {
      isFollowing: boolean
      isFollower: boolean
      isMutualFollow: boolean
      followTime?: string
      remarkName?: string
    }
  }>({
    url: '/app/imUserFollow/check-relation',
    data: {
      userId,
      targetUserId,
    },
  })
}

/**
 * 批量关注用户
 */
export const batchFollowUsers = (userId: string, followUserIds: string[], groupId?: string) => {
  return request.post<{ code: number; message: string }>({
    url: '/app/imUserFollow/batch-follow',
    data: {
      userId,
      followUserIds,
      groupId,
    },
  })
}

/**
 * 批量取消关注
 */
export const batchUnfollowUsers = (userId: string, followUserIds: string[]) => {
  return request.post<{ code: number; message: string }>({
    url: '/app/imUserFollow/batch-unfollow',
    data: {
      userId,
      followUserIds,
    },
  })
}

/**
 * 移动关注到分组
 */
export const moveFollowToGroup = (followId: string, groupId: string) => {
  return request.post<{ code: number; message: string }>({
    url: '/app/imUserFollow/move-to-group',
    data: {
      followId,
      groupId,
    },
  })
}

/**
 * 获取关注分组列表
 */
export const getFollowGroups = (userId: string) => {
  return request.get<{
    code: number
    message: string
    data: FollowGroup[]
  }>({
    url: '/app/imUserFollow/groups',
    data: {
      userId,
    },
  })
}

/**
 * 创建关注分组
 */
export const createFollowGroup = (userId: string, groupName: string, groupOrder?: number) => {
  return request.post<{ code: number; message: string; data: { groupId: string } }>({
    url: '/app/imUserFollow/create-group',
    data: {
      userId,
      groupName,
      groupOrder,
    },
  })
}

/**
 * 删除关注分组
 */
export const deleteFollowGroup = (groupId: string) => {
  return request.post<{ code: number; message: string }>({
    url: '/app/imUserFollow/delete-group',
    data: {
      groupId,
    },
  })
}

/**
 * 获取关注统计信息
 */
export const getFollowStatistics = (userId: string) => {
  return request.get<{
    code: number
    message: string
    data: {
      followingCount: number
      followerCount: number
      mutualFollowCount: number
      groupStats: Array<{ groupId: string; groupName: string; count: number }>
      recentFollows: Array<{ userId: string; userName: string; followTime: string }>
    }
  }>({
    url: '/app/imUserFollow/statistics',
    data: {
      userId,
    },
  })
}
