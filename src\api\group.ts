/**
 * 群组管理API
 */
import { request } from '@/utils/request'

export interface GroupInfo {
  id: string
  name: string
  avatar?: string
  description?: string
  memberCount: number
  maxMemberCount?: number
  ownerId: string
  ownerNickname?: string
  createTime: string
  updateTime?: string
  status: 'active' | 'disbanded' | 'frozen'
  isPublic: boolean
  joinApproval: boolean // 是否需要审批加入
  remarkName?: string // 群备注
  groupRemark?: string // 我在群里的备注
  myNickname?: string // 我在群里的昵称
  doNotDisturb?: boolean // 免打扰设置
  isTop?: boolean // 是否置顶
  showMemberNickname?: boolean // 是否显示群成员昵称
}

export interface GroupResponse {
  code: number
  message: string
  data: GroupInfo
}

export interface GroupListResponse {
  code: number
  message: string
  data: GroupInfo[]
  total?: number
}

export interface GroupPageParams {
  current?: number
  size?: number
  keyword?: string
  status?: string
}

/**
 * 修改群组信息
 */
export const updateGroup = (groupId: string, data: Partial<GroupInfo>) => {
  return request<{ code: number; message: string }>({
    url: `/app/imGroup`,
    method: 'PUT',
    data: {
      id: groupId,
      ...data,
    },
  })
}

/**
 * 新增群组
 */
export const createGroup = (data: {
  name: string
  description?: string
  avatar?: string
  isPublic?: boolean
  joinApproval?: boolean
  maxMemberCount?: number
  memberIds?: string[]
}) => {
  return request<{ code: number; message: string; data: { groupId: string } }>({
    url: '/app/imGroup',
    method: 'POST',
    data,
  })
}

/**
 * 通过id删除群组
 */
export const deleteGroup = (groupId: string) => {
  return request<{ code: number; message: string }>({
    url: '/app/imGroup',
    method: 'DELETE',
    data: {
      id: groupId,
    },
  })
}

/**
 * 设置我在群里的昵称
 */
export const updateMyGroupNickname = (groupId: string, nickname: string) => {
  return request<{ code: number; message: string }>({
    url: '/app/imGroup/updateRemarkName',
    method: 'POST',
    data: {
      groupId,
      remarkName: nickname,
    },
  })
}

/**
 * 设置群备注
 */
export const updateGroupRemark = (groupId: string, remark: string) => {
  return request<{ code: number; message: string }>({
    url: '/app/imGroup/updateRemarkGroupName',
    method: 'POST',
    data: {
      groupId,
      remarkGroupName: remark,
    },
  })
}

/**
 * 修改群组名称
 */
export const updateGroupName = (groupId: string, name: string) => {
  return request<{ code: number; message: string }>({
    url: '/app/imGroup/updateGroupName',
    method: 'POST',
    data: {
      groupId,
      groupName: name,
    },
  })
}

/**
 * 设置显示群成员昵称
 */
export const updateGroupMemberNicknameDisplay = (groupId: string, showNickname: boolean) => {
  return request<{ code: number; message: string }>({
    url: '/app/imGroup/remarkFlagConfig',
    method: 'POST',
    data: {
      groupId,
      showNickname,
    },
  })
}

/**
 * 群组设置免打扰
 */
export const updateGroupDoNotDisturb = (groupId: string, doNotDisturb: boolean) => {
  return request<{ code: number; message: string }>({
    url: '/app/imGroup/noDisturbingConfig',
    method: 'POST',
    data: {
      groupId,
      doNotDisturb,
    },
  })
}

/**
 * 批量导入群组
 */
export const importGroups = (groups: Array<Partial<GroupInfo>>) => {
  return request<{ code: number; message: string }>({
    url: '/app/imGroup/import',
    method: 'POST',
    data: {
      groups,
    },
  })
}

/**
 * 查询群组/加入群组
 */
export const searchAndJoinGroup = (keyword: string) => {
  return request<{ code: number; message: string; data: GroupInfo[] }>({
    url: '/app/imGroup/consult',
    method: 'POST',
    data: {
      keyword,
    },
  })
}

/**
 * 取消群组设置免打扰
 */
export const cancelGroupDoNotDisturb = (groupId: string) => {
  return request<{ code: number; message: string }>({
    url: '/app/imGroup/cancelNoDisturbingConfig',
    method: 'POST',
    data: {
      groupId,
    },
  })
}

/**
 * 分页查询群组
 */
export const getGroupsPage = (params: GroupPageParams = {}) => {
  return request<GroupListResponse>({
    url: '/app/imGroup/page',
    method: 'GET',
    params: {
      current: params.current || 1,
      size: params.size || 20,
      keyword: params.keyword,
      status: params.status,
    },
  })
}

/**
 * 查询群组配置设置
 */
export const getGroupConfig = (groupId: string) => {
  return request<{
    code: number
    message: string
    data: {
      doNotDisturb: boolean
      showMemberNickname: boolean
      remarkName?: string
      groupRemark?: string
    }
  }>({
    url: '/app/imGroup/getGroupConfig',
    method: 'GET',
    params: {
      groupId,
    },
  })
}

/**
 * 导出群组列表
 */
export const exportGroups = () => {
  return request<Blob>({
    url: '/app/imGroup/export',
    method: 'GET',
    responseType: 'blob',
  })
}

/**
 * 通过id查询群组详情
 */
export const getGroupDetails = (groupId: string) => {
  return request<GroupResponse>({
    url: '/app/imGroup/details',
    method: 'GET',
    params: {
      id: groupId,
    },
  })
}

/**
 * 获取我加入的群组列表
 */
export const getMyGroups = () => {
  return request<GroupListResponse>({
    url: '/app/imGroup/my-groups',
    method: 'GET',
  })
}

/**
 * 退出群组
 */
export const leaveGroup = (groupId: string) => {
  return request<{ code: number; message: string }>({
    url: '/app/imGroup/leave',
    method: 'POST',
    data: {
      groupId,
    },
  })
}

/**
 * 解散群组（仅群主）
 */
export const disbandGroup = (groupId: string) => {
  return request<{ code: number; message: string }>({
    url: '/app/imGroup/disband',
    method: 'POST',
    data: {
      groupId,
    },
  })
}

/**
 * 转让群主
 */
export const transferGroupOwnership = (groupId: string, newOwnerId: string) => {
  return request<{ code: number; message: string }>({
    url: '/app/imGroup/transfer-ownership',
    method: 'POST',
    data: {
      groupId,
      newOwnerId,
    },
  })
}

/**
 * 设置群组公开状态
 */
export const updateGroupPublicStatus = (groupId: string, isPublic: boolean) => {
  return request<{ code: number; message: string }>({
    url: '/app/imGroup/public-status',
    method: 'POST',
    data: {
      groupId,
      isPublic,
    },
  })
}

/**
 * 设置群组加入审批
 */
export const updateGroupJoinApproval = (groupId: string, joinApproval: boolean) => {
  return request<{ code: number; message: string }>({
    url: '/app/imGroup/join-approval',
    method: 'POST',
    data: {
      groupId,
      joinApproval,
    },
  })
}
