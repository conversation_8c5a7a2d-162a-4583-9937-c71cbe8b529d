/**
 * 消息本地存储工具
 */

export interface StoredMessage {
  id: string
  type: string
  content: string
  from: string
  to: string
  timestamp: number
  isOwn: boolean
  avatar: string
  nickname: string
  isRead: boolean
  conversationId: string
}

const MESSAGE_STORAGE_KEY = 'chat_messages'
const MAX_MESSAGES_PER_CONVERSATION = 1000 // 每个会话最多存储1000条消息

/**
 * 获取本地存储的消息
 */
export function getStoredMessages(): Record<string, StoredMessage[]> {
  try {
    const stored = uni.getStorageSync(MESSAGE_STORAGE_KEY)
    return stored || {}
  } catch (error) {
    console.error('获取本地消息失败:', error)
    return {}
  }
}

/**
 * 获取指定会话的消息
 */
export function getConversationMessages(conversationId: string): StoredMessage[] {
  const allMessages = getStoredMessages()
  return allMessages[conversationId] || []
}

/**
 * 保存消息到本地
 */
export function saveMessage(message: StoredMessage): void {
  try {
    const allMessages = getStoredMessages()
    
    if (!allMessages[message.conversationId]) {
      allMessages[message.conversationId] = []
    }
    
    const conversationMessages = allMessages[message.conversationId]
    
    // 检查消息是否已存在
    const existingIndex = conversationMessages.findIndex(msg => msg.id === message.id)
    
    if (existingIndex !== -1) {
      // 更新已存在的消息
      conversationMessages[existingIndex] = message
    } else {
      // 添加新消息
      conversationMessages.push(message)
      
      // 按时间戳排序
      conversationMessages.sort((a, b) => a.timestamp - b.timestamp)
      
      // 限制消息数量，删除最旧的消息
      if (conversationMessages.length > MAX_MESSAGES_PER_CONVERSATION) {
        conversationMessages.splice(0, conversationMessages.length - MAX_MESSAGES_PER_CONVERSATION)
      }
    }
    
    allMessages[message.conversationId] = conversationMessages
    uni.setStorageSync(MESSAGE_STORAGE_KEY, allMessages)
    
    console.log('消息已保存到本地:', message.id)
  } catch (error) {
    console.error('保存消息到本地失败:', error)
  }
}

/**
 * 批量保存消息
 */
export function saveMessages(messages: StoredMessage[]): void {
  messages.forEach(message => saveMessage(message))
}

/**
 * 删除会话的所有消息
 */
export function deleteConversationMessages(conversationId: string): void {
  try {
    const allMessages = getStoredMessages()
    delete allMessages[conversationId]
    uni.setStorageSync(MESSAGE_STORAGE_KEY, allMessages)
    console.log('已删除会话消息:', conversationId)
  } catch (error) {
    console.error('删除会话消息失败:', error)
  }
}

/**
 * 清空所有本地消息
 */
export function clearAllMessages(): void {
  try {
    uni.removeStorageSync(MESSAGE_STORAGE_KEY)
    console.log('已清空所有本地消息')
  } catch (error) {
    console.error('清空本地消息失败:', error)
  }
}

/**
 * 标记消息为已读
 */
export function markMessageAsRead(conversationId: string, messageId: string): void {
  try {
    const allMessages = getStoredMessages()
    const conversationMessages = allMessages[conversationId]
    
    if (conversationMessages) {
      const message = conversationMessages.find(msg => msg.id === messageId)
      if (message) {
        message.isRead = true
        uni.setStorageSync(MESSAGE_STORAGE_KEY, allMessages)
      }
    }
  } catch (error) {
    console.error('标记消息已读失败:', error)
  }
}

/**
 * 标记会话所有消息为已读
 */
export function markConversationAsRead(conversationId: string): void {
  try {
    const allMessages = getStoredMessages()
    const conversationMessages = allMessages[conversationId]
    
    if (conversationMessages) {
      conversationMessages.forEach(message => {
        message.isRead = true
      })
      uni.setStorageSync(MESSAGE_STORAGE_KEY, allMessages)
    }
  } catch (error) {
    console.error('标记会话已读失败:', error)
  }
}
